import { fixupConfigRules } from "@eslint/compat";
import jsdoc from "eslint-plugin-jsdoc";
import preferArrow from "eslint-plugin-prefer-arrow";
import globals from "globals";
import path from "node:path";
import { fileURLToPath } from "node:url";
import js from "@eslint/js";
import { FlatCompat } from "@eslint/eslintrc";

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const compat = new FlatCompat({
    baseDirectory: __dirname,
    recommendedConfig: js.configs.recommended,
    allConfig: js.configs.all
});

export default [...fixupConfigRules(compat.extends(
    "eslint:recommended",
    "plugin:react/recommended",
    "plugin:react/jsx-runtime",
    "plugin:react-hooks/recommended",
    "plugin:storybook/recommended",
    "prettier",
)), {
    plugins: {
        jsdoc,
        "prefer-arrow": preferArrow,
    },

    languageOptions: {
        globals: {
            ...globals.browser,
            ...globals.node,
        },

        ecmaVersion: 13,
        sourceType: "module",
    },

    settings: {
        react: {
            version: "detect",
        },
    },

    rules: {
        "prefer-arrow/prefer-arrow-functions": ["warn", {
            disallowPrototype: true,
            singleReturnOnly: false,
            classPropertiesAllowed: false,
        }],

        "no-unused-vars": ["error", {
            argsIgnorePattern: "^_",
        }],

        "react/no-unknown-property": ["error", {
            ignore: ["tw", "css"],
        }],
    },
}];