import { useEffect, useState } from 'react';
import resolveConfig from 'tailwindcss/resolveConfig';
import tailwindConfig from '../../tailwind.config.js';

const fullConfig = resolveConfig(tailwindConfig);
const twScreens = fullConfig.theme?.screens;

const breakpoints = Object.entries(twScreens || {}).map(([key, value]) => ({
  id: key,
  minWidth: typeof value === 'number' ? value : parseInt(value),
}));

const sortedBreakpoints = breakpoints.sort((a, b) => a.minWidth - b.minWidth);

const useBreakPoint = () => {
  const [breakpoint, setBreakpoint] = useState();

  useEffect(() => {
    const handleResize = () => {
      const width = window.innerWidth;
      for (let i = 0; i < sortedBreakpoints.length; i++) {
        const { minWidth, id } = sortedBreakpoints[i];
        if (width < minWidth) {
          if (i > 0 && sortedBreakpoints[i - 1].id !== breakpoint) {
            setBreakpoint(sortedBreakpoints[i - 1].id);
          }
          break;
        }
        if (i === sortedBreakpoints.length - 1 && id !== breakpoint) {
          setBreakpoint(id);
        }
      }
    };

    handleResize();
    window.addEventListener('resize', handleResize);

    return () => window.removeEventListener('resize', handleResize);
  }, [breakpoint]);

  return breakpoint;
};

export default useBreakPoint;
