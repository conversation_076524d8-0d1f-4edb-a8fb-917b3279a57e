import { redirect, json } from 'react-router-dom';

import { VerifyAccountPage } from '../pages/auth';
import {
  handleSignUpStep,
  confirmAccount,
  createUserSession,
  login,
} from '../utils/session';
import { badRequest } from '../utils/request';
import { decryptData } from '../utils/common';

export const verifyAccountLoader = ({ request }) => {
  const url = new URL(request.url);
  const email = url.searchParams.get('email');
  const source = url.searchParams.get('from');
  const enp = sessionStorage.getItem('enp');

  if (!email || !source || (source === 'signin' && !enp)) {
    return redirect('/signin');
  }

  return json({
    fields: {
      code: '',
    },
  });
};

export const verifyAccountAction = async ({ request }) => {
  const form = await request.formData();
  const email = form.get('email');
  const code = form.get('code');
  const source = form.get('source');
  const fields = { code };

  if (typeof email !== 'string' || typeof code !== 'string') {
    return badRequest({
      fieldErrors: null,
      fields: null,
      formError: 'Form was not submitted correctly.',
    });
  }

  const fieldErrors = {
    code: code.length === 0 ? 'Required' : null,
  };

  if (Object.values(fieldErrors).some(Boolean)) {
    const errorCopy = 'Please enter the confirmation code found in your email.';
    return badRequest({
      fieldErrors,
      fields,
      formError: errorCopy,
    });
  }

  let verificationRes;
  try {
    verificationRes = await confirmAccount({ email, code });
  } catch (error) {
    return badRequest({
      fieldErrors: { code: true },
      fields,
      formError: error.message,
    });
  }

  if (source.toLowerCase() === 'signin') {
    const enp = sessionStorage.getItem('enp');
    const dep = decryptData(enp);
    let tokenRes;
    try {
      tokenRes = await login(email, dep);
    } catch (error) {
      return badRequest({
        fieldErrors: null,
        fields,
        formError: error.message,
      });
    }
    sessionStorage.removeItem('enp');
    /**
     * SPECIAL CASE: manually set the session cookie here and let the
     * AuthLayout loader handle the redirect
     */
    createUserSession(tokenRes.token, tokenRes.idToken);
    return json({ token: tokenRes.token });
  }

  return handleSignUpStep(verificationRes.nextStep);
};

const VerifyAccount = () => {
  return <VerifyAccountPage />;
};

export default VerifyAccount;
