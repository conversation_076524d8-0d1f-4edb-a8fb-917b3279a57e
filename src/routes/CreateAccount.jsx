import { json } from 'react-router-dom';

import { CreateAccountPage } from '../pages/auth';
import { register, handleSignUpStep } from '../utils/session';
import { badRequest } from '../utils/request';
import { validateEmail, validatePassword } from '../utils/validate';
import API from '../utils/api';

const { AccessApi } = API;

export const createAccountLoader = async () => {
  return json({
    fields: {
      email: '',
      password: '',
      firstName: '',
      lastName: '',
    },
  });
};

export const createAccountAction = async ({ request }) => {
  const form = await request.formData();
  const email = form.get('email').toLowerCase();
  const password = form.get('password');
  const firstName = form.get('firstName').trim();
  const lastName = form.get('lastName').trim();
  const fields = { email, password, firstName, lastName };

  if (
    typeof email !== 'string' ||
    typeof password !== 'string' ||
    typeof firstName !== 'string' ||
    typeof lastName !== 'string'
  ) {
    return badRequest({
      fieldErrors: null,
      fields: null,
      formError: 'Form was not submitted correctly.',
    });
  }

  const fieldErrors = {
    email: validateEmail(email),
    password: validatePassword(password),
    firstName: firstName.length === 0 ? 'Required' : null,
    lastName: lastName.length === 0 ? 'Required' : null,
  };

  if (Object.values(fieldErrors).some(Boolean)) {
    const errorCopy =
      'We cannot complete your account creation. Please check the signup form for missing or invalid fields and try again or contact your administrator.';
    return badRequest({
      fieldErrors,
      fields,
      formError: errorCopy,
    });
  }

  // Check if the email exists in the whitelist.
  const isInWhitelist = await AccessApi.checkIsEmailWhiteListed(email);
  if (!isInWhitelist?.access) {
    return badRequest({
      fieldErrors: {
        email: true,
      },
      fields,
      formError:
        'Sorry we are unable to sign you up. Please contact your administrator.',
    });
  }

  let registerRes;
  try {
    const signupData = {
      email,
      password,
      firstName,
      lastName,
    };
    registerRes = await register(signupData);
  } catch (error) {
    return badRequest({
      fieldErrors: null,
      fields,
      formError:
        'There was a problem creating your account. Check the form fields and please try again.',
    });
  }

  return handleSignUpStep(registerRes.nextStep, email);
};

const CreateAccount = () => {
  return <CreateAccountPage />;
};

export default CreateAccount;
