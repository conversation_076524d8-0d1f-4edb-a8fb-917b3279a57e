import PeopleManagementPage from '../pages/PeopleManagement.jsx';
import { requireAuth } from '../utils/session.js';

export const peopleManagementAction = async ({ request }) => {
  const formData = await request.formData();
  const intent = formData.get('intent');

  switch (intent) {
    default:
      break;
  }
};

export const peopleManagementLoader = async () => {
  await requireAuth();
  return {};
};

const PeopleManagement = () => {
  return (
    <>
      <PeopleManagementPage />
    </>
  );
};

export default PeopleManagement;
