import { json } from 'react-router-dom';

import { SignInPage } from '../pages/auth';
import { login, createUserSession } from '../utils/session';
import { badRequest } from '../utils/request';
import { encryptData } from '../utils/common';
import {
  getResetPasswordStatus,
  removeResetPasswordStatus,
} from '../stores/auth';

export const signInLoader = async () => {
  // Clear sessionStorage from any previous actions
  sessionStorage.clear();
  const resetPasswordStatus = await getResetPasswordStatus();
  return json({
    fields: {
      email: '',
      password: '',
    },
    resetPasswordStatus,
  });
};

export const signInAction = async ({ request }) => {
  // Clear statuses from previous actions
  await removeResetPasswordStatus();
  const form = await request.formData();
  const email = form.get('email');
  const password = form.get('password');
  const fields = { email, password };

  if (typeof email !== 'string' || typeof password !== 'string') {
    return badRequest({
      fieldErrors: null,
      fields: null,
      formError: 'Form was not submitted correctly.',
    });
  }

  let tokenRes;
  try {
    tokenRes = await login(email, password);
  } catch (error) {
    return badRequest({
      fieldErrors: null,
      fields,
      formError: error.message,
    });
  }

  // If we get a redirect response, return it so the router
  // can follow the redirect and load the signup verification page.
  if (tokenRes?.status === 302) {
    try {
      const en = encryptData(password);
      sessionStorage.setItem('enp', en);
    } catch (error) {
      return badRequest({
        fieldErrors: null,
        fields,
        formError: 'Something went wrong. Please try again.',
      });
    }
    return tokenRes;
  }

  if (!tokenRes || !tokenRes.token) {
    // Throw some kind of error Response here
    return badRequest({
      fieldErrors: null,
      fields,
      formError: 'Incorrect username or password.',
    });
  }

  /*
   * SPECIAL CASE: manually set the session cookie here and let the
   * AuthLayout loader handle the redirect
   */
  createUserSession(tokenRes.token, tokenRes.idToken);
  return json({ token: tokenRes.token });
};

const SignIn = () => {
  return <SignInPage />;
};

export default SignIn;
