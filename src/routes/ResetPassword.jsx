import { json } from 'react-router-dom';

import { ResetPasswordPage } from '../pages/auth';
import { confirmResetPasswordRequest } from '../utils/session';
import { badRequest } from '../utils/request';
import { validatePassword } from '../utils/validate';
import { setResetPasswordStatus } from '../stores/auth';

export const resetPasswordLoader = () => {
  return json({
    fields: {
      resetCode: '',
      newPassword: '',
    },
  });
};

export const resetPasswordAction = async ({ request }) => {
  const form = await request.formData();
  const email = form.get('email');
  const resetCode = form.get('resetCode');
  const newPassword = form.get('newPassword');
  const fields = { resetCode, newPassword };

  if (typeof resetCode !== 'string' || typeof newPassword !== 'string') {
    return badRequest({
      fieldErrors: null,
      fields: null,
      formError: 'Form was not submitted correctly.',
    });
  }

  const fieldErrors = {
    newPassword: validatePassword(newPassword),
  };

  if (Object.values(fieldErrors).some(Boolean)) {
    return badRequest({
      fieldErrors,
      fields,
      formError:
        'There was an issue processing your request. Please check the fields entered and try again.',
    });
  }

  try {
    const resetPasswordOutput = await confirmResetPasswordRequest({
      email,
      code: resetCode,
      password: newPassword,
    });

    // If we get a null output here, we got an unrecognized status from cognito
    if (!resetPasswordOutput) {
      return badRequest({
        fieldErrors: null,
        fields,
        formError: 'Somethig went wrong. Please try again later.',
      });
    }

    await setResetPasswordStatus('success');
    return resetPasswordOutput;
  } catch (error) {
    return badRequest({
      fieldErrors: null,
      fields,
      formError: error.message,
    });
  }
};

const ResetPassword = () => {
  return <ResetPasswordPage />;
};

export default ResetPassword;
