import { Suspense, useState, useEffect } from 'react';
import {
  Outlet,
  defer,
  Await,
  useLoaderData,
  useRevalidator,
  redirect,
} from 'react-router-dom';
import styled from 'styled-components';
import tw from 'twin.macro';

import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Header, DSLoadingSpinner } from '../components';
import { requireAuth, logout } from '../utils/session';
import API from '../utils/api';
import { reconcileNavItems } from '../utils/common';
import useBreakPoint from '../hooks/useBreakPoint';
import { SiteSelectorProvider } from '../contexts/SiteSelectorContext';
import { ScheduleProvider } from '../contexts/ScheduleContext';

const { getAuthenticatedUserInfo, getSiteGroupInfo } = API;

const MOBILE_SITES = [
  { site_id: 'DST00001', title: 'Mobile Pod 1' },
  { site_id: 'DST00003', title: 'Mobile Pod 3' },
];

const StyledDashboardLayout = styled.div`
  ${tw`
    flex flex-col
    h-full
    w-full
    overflow-x-hidden
  `};
`;

const StyledOutletContainer = styled.div`
  ${tw`
    md:flex 
    md:flex-row
    md:flex-1
    h-full
    pt-[5.375rem]
  `};
`;

const StyledInnerOutletContainer = styled.div`
  ${tw`
    flex-1
    overflow-y-auto
    transition-all duration-300 ease-in-out
    z-1
  `};

  .dsl-outlet-content {
    ${tw`
      h-full
    `}
  }
`;

export const dashboardLayoutAction = async ({ request }) => {
  const formData = await request.formData();
  const intent = formData.get('intent');

  switch (intent) {
    case 'logout': {
      return logout();
    }
    default:
      break;
  }
};

export const dashboardLayoutLoader = async ({ request }) => {
  const tokenData = await requireAuth();
  const userInfo = await getAuthenticatedUserInfo();
  const allSites = await Promise.all(
    Object.keys(userInfo.sites).map(async (siteId) => {
      const siteInfo = await getSiteGroupInfo(siteId);
      return siteInfo[0];
    }),
  );
  const userData = {
    name: userInfo.profile['First Name'] + ' ' + userInfo.profile['Last Name'],
    email: userInfo.user_email,
    first_name: userInfo.profile['First Name'],
    last_name: userInfo.profile['Last Name'],
    roles: Object.entries(userInfo.sites).map(([siteId, siteInfo]) => ({
      site_id: siteId,
      role: siteInfo.mrm_role,
    })),
  };

  const mobileSites = allSites[0]?.sub_sites.map((mobileSite) => ({
    site_id: mobileSite.site_id,
  }));

  const activeNavItems = reconcileNavItems({
    currentRole: userData.roles?.[0]?.role,
  });

  const uri = new URL(request.url);
  let selectedNonActiveNavItem = true;
  for (const item of activeNavItems) {
    const urlPath = item.path ? `/app/${item.path}` : '/app';
    if (request.url.match(urlPath)?.length) {
      selectedNonActiveNavItem = false;
    }
  }

  if (selectedNonActiveNavItem) {
    const redirectPath = activeNavItems.length
      ? activeNavItems[0].path
      : 'dashboard';
    const redirectUrl = redirectPath ? `/app/${redirectPath}` : '/app';
    if (uri.pathname !== redirectUrl) {
      return redirect(redirectUrl);
    }
  }

  return defer({
    ...tokenData,
    userData,
    allSites,
    mobileSites,
  });
};

const DashboardLayout = () => {
  const loaderData = useLoaderData();
  const revalidator = useRevalidator();
  const breakpoint = useBreakPoint();
  const isMobile = breakpoint === 'xs' || breakpoint === 'sm';
  const userData = loaderData.userData;

  const [isSideMenuOpen, setIsSideMenuOpen] = useState(false);

  useEffect(() => {
    if (isMobile) {
      setIsSideMenuOpen(false);
    }
  }, [isMobile]);

  const commonData = new Promise((resolve, reject) => {
    const allResolvedPromise = Promise.all([loaderData.allSites, loaderData.mobileSites]);

    return allResolvedPromise
      .then((allResolved) => {
        const [allSites, mobileSites] = allResolved;
        return resolve({
          allSites,
          mobileSites
        });
      })
      .catch((err) => {
        return reject(err);
      });
  });

  const onRefresh = () => {
    if (revalidator.state === 'idle') {
      revalidator.revalidate();
    }
  };

  return (
    <StyledDashboardLayout>
      <ScheduleProvider>
        <SiteSelectorProvider>
          <Header
            userData={userData}
            isSideMenuOpen={isSideMenuOpen}
            setIsSideMenuOpen={setIsSideMenuOpen}
          />
          <StyledOutletContainer>
            <DSNavContainer
              isSideMenuOpen={isSideMenuOpen}
              userRole={userData.roles?.[0]?.role}
            />
            <StyledInnerOutletContainer>
              <Suspense
                fallback={
                  <div tw="w-full h-full flex items-center justify-center">
                    <DSLoadingSpinner />
                  </div>
                }
              >
                <Await resolve={commonData}>
                  <div className="dsl-outlet-content">
                    <Outlet context={{ userData, onRefresh }} />
                  </div>
                </Await>
              </Suspense>
            </StyledInnerOutletContainer>
          </StyledOutletContainer>
        </SiteSelectorProvider>
      </ScheduleProvider>
    </StyledDashboardLayout>
  );
};

export default DashboardLayout;
