import { json } from 'react-router-dom';

import { ForgotPasswordPage } from '../pages/auth';
import { resetPasswordRequest } from '../utils/session';
import { badRequest } from '../utils/request';
import { validateEmail } from '../utils/validate';

export const forgotPasswordLoader = () => {
  return json({
    fields: {
      email: '',
    },
  });
};

export const forgotPasswordAction = async ({ request }) => {
  const form = await request.formData();
  const email = form.get('email');
  const fields = { email };

  if (typeof email !== 'string') {
    return badRequest({
      fieldErrors: null,
      fields: null,
      formError: 'Form was not submitted correctly.',
    });
  }

  const fieldErrors = {
    email: validateEmail(email),
  };

  if (Object.values(fieldErrors).some(Boolean)) {
    return badRequest({
      fieldErrors,
      fields,
      formError:
        'There was an issue processing your request. Please check the email entered and try again.',
    });
  }

  try {
    const forgotPasswordOutput = await resetPasswordRequest({ email });

    // If we get a null output here, we got an unrecognized status from cognito
    if (!forgotPasswordOutput) {
      return badRequest({
        fieldErrors: null,
        fields,
        formError: 'Somethig went wrong. Please try again later.',
      });
    }

    return forgotPasswordOutput;
  } catch (error) {
    return badRequest({
      fieldErrors: null,
      fields,
      formError: error.message,
    });
  }
};

const ForgotPassword = () => {
  return <ForgotPasswordPage />;
};

export default ForgotPassword;
