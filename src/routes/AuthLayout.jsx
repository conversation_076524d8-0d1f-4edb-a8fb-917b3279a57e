import { Outlet, redirect, json } from 'react-router-dom';
import styled from 'styled-components';
import tw from 'twin.macro';

import { requireAuth, setSession } from '../utils/session';

import imgDSLearnLogo from '../assets/ds-learn-logo.png';
import imgMRMLogo from '../assets/MRM_Logo.svg';

const StyledSigninPageContainer = styled.div`
  ${tw`
    flex flex-col items-center
    bg-dslBlack-2
    text-dslWhite
    p-10
    min-h-full
  `};
`;

const StyledBrandingSection = styled.div`
  ${tw`
    flex flex-col
    flex-1
    items-center
    py-5 px-11
  `};

  .dsl-learn-logo {
    ${tw`w-44`};
  }

  .dsl-mrm-logo {
    ${tw`w-96`};
  }
`;

const StyledFooterSection = styled.div`
  ${tw`
    flex flex-1
    justify-center items-end
    text-dslWhite
    text-2xs
    sm:text-xs text-center
    mt-12
    opacity-50
  `};
`;

export const authLoader = async ({ request }) => {
  try {
    const { token, idToken } = await requireAuth();

    // Normal Cognito handling
    if (token && idToken) {
      setSession(token, idToken, null);
      const searchParams = new URL(request.url).searchParams;
      const redirectTo = searchParams.get('redirectTo');
      if (redirectTo) {
        return redirect(redirectTo);
      }
      return redirect('/app/dashboard');
    }
  } catch (error) {
    // requireAuth() will throw a redirect response if the user is not authenticated
    // so we can ignore the error here and let the redirect response bubble up
  }

  return json({
    fields: { email: '', password: '' },
  });
};

const AuthLayout = () => {
  const date = new Date();
  return (
    <StyledSigninPageContainer>
      <div tw="flex flex-col mt-10">
        <StyledBrandingSection>
          <div tw="flex flex-col h-[160px] justify-between items-center">
            <img className="dsl-learn-logo" src={imgDSLearnLogo} />
            <img className="dsl-mrm-logo" src={imgMRMLogo} />
          </div>
        </StyledBrandingSection>

        {/* Page Form */}
        <Outlet />
      </div>
      <StyledFooterSection>
        <div>
          Copyright © {date.getFullYear()} Dreamscape Learn. All rights
          reserved.
        </div>
      </StyledFooterSection>
    </StyledSigninPageContainer>
  );
};

export default AuthLayout;
