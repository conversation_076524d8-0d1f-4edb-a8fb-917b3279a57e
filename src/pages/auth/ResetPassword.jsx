import { useState } from 'react';
import styled from 'styled-components';
import tw from 'twin.macro';
import {
  Link,
  Form,
  useActionData,
  useSearchParams,
  useNavigation,
} from 'react-router-dom';

import {
  DSInput,
  DSButton,
  DSLoadingSpinner,
  DSBanner,
} from '../../components';
import { EyeOff, EyeOn } from '../../components/svg/icons';
import { BTN_TYPES } from '../../components/DSButton';

const StyledWelcomeSection = styled.div`
  ${tw`
    flex flex-col
    items-center
    px-4
    text-center
  `};

  h3 {
    ${tw`
      font-medium
      text-2xl
      leading-[2.375rem]
      py-2.5
    `};
  }

  h5 {
    ${tw`
      font-normal
      text-sm text-center
      opacity-80
      sm:w-2/5
    `};
  }
`;

const StyledFormSection = styled.div`
  ${tw`
    flex flex-1
    justify-center
    px-3
  `};
`;

const StyledFormContainer = styled.div`
  ${tw`
    flex flex-col
    w-[21.25rem]
    max-w-[18.75rem]
    items-end
    gap-4
    text-sm
  `};
`;

const StyledMainContainer = styled.div`
  ${tw`
      relative
      flex flex-col
      gap-7.5
      px-3
      max-w-[100vw]
    `};
  .dsl-error-info {
    ${tw`
        px-3
      `};
  }
  .dsl-sign-in-link {
    ${tw`
          flex
          justify-center
          text-sm
      `};
    a {
      ${tw`
          max-w-[25ch]
          md:max-w-full
          text-center  
        `}
    }
  }
  .dsl-hint-area {
    ${tw`
        text-2xs
        leading-normal
        mt-0.5
        text-dslGray-3
        whitespace-pre-line
      `};
  }
`;

const hintCopy = `Password requirements:
  - Min length: 8 characters
  - Contain at least 1 number
  - Contain at least 1 symbol like "?$%^&).
  - Contain at least 1 uppercase letter
  - Contain at least 1 lowercase letter`;
const headingCopy = 'Password reset code';
const buttonLabelCopy = 'Reset Password';
const signinLinkCopy = 'Sent to the wrong email? Go back to sign in';
const bannerCopy =
  'If this email address was used to create an account, instructions to reset your password have been sent to you. Please check your email.';
const formId = 'reset-password-form';

const ResetPasswordPage = () => {
  const navigation = useNavigation();
  const fetcherData = useActionData('auth/reset-password');
  const [searchParams] = useSearchParams();
  const [isPasswordHidden, setIsPasswordHidden] = useState(true);
  const [resetCodeHasValue, setResetCodeHasValue] = useState(false);
  const [newPassHasValue, setNewPassHasValue] = useState(false);

  const buttonDisabled =
    !resetCodeHasValue || !newPassHasValue || navigation.state !== 'idle';
  const hasError = navigation.state === 'idle' && !!fetcherData?.formError;
  const showInfoBanner = !hasError && navigation.state === 'idle';

  return (
    <StyledMainContainer>
      <div className="dsl-error-info">
        <DSBanner body={bannerCopy} variant="info" isVisible={showInfoBanner} />
        <DSBanner
          body={fetcherData?.formError || ''}
          variant="error"
          isVisible={hasError}
        />
      </div>
      <StyledWelcomeSection>
        <h3>{headingCopy}</h3>
         <h5>{`We have sent a password reset code to ${searchParams.get('email') ?? ''}. Please enter the code you received below:`}</h5>
      </StyledWelcomeSection>
      <StyledFormSection>
        <Form key="auth-reset-password" id={formId} method="post">
          <input type="hidden" name="email" value={searchParams.get('email')} />
          <StyledFormContainer>
            <div tw="w-full">
              <DSInput
                dark
                defaultValue={fetcherData?.fields?.resetCode}
                error={fetcherData?.fieldErrors?.resetCode}
                placeholder="Password reset code"
                onChange={(e) =>
                  setResetCodeHasValue(e.target.value.length > 0)
                }
                type="text"
                name="resetCode"
              />
            </div>
            <div tw="w-full">
              <DSInput
                dark
                defaultValue={fetcherData?.fields?.newPassword}
                error={!!fetcherData?.fieldErrors?.newPassword}
                placeholder="Enter new password"
                type={isPasswordHidden ? 'password' : 'text'}
                name="newPassword"
                onChange={(e) => setNewPassHasValue(e.target.value.length > 0)}
                endIconOnClick={() => setIsPasswordHidden(!isPasswordHidden)}
                endIconElement={isPasswordHidden ? EyeOff : EyeOn}
              />
              <div className="dsl-hint-area">{hintCopy}</div>
            </div>
            <DSButton
              form={formId}
              variant={
                !buttonDisabled ? BTN_TYPES.primary : BTN_TYPES.secondary
              }
              label={buttonLabelCopy}
              disabled={buttonDisabled}
              fullWidth
              dark
              icon={
                navigation.state !== 'idle' ? <DSLoadingSpinner /> : undefined
              }
              iconAutoWidth
            />
          </StyledFormContainer>
        </Form>
      </StyledFormSection>
      <div className="dsl-sign-in-link">
        <Link to="/signin">{signinLinkCopy}</Link>
      </div>
    </StyledMainContainer>
  );
};

export default ResetPasswordPage;
