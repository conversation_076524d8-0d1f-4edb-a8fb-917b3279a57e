import styled from 'styled-components';
import tw from 'twin.macro';
import {
  Link,
  useActionData,
  useNavigation,
  useSearchParams,
  Form,
} from 'react-router-dom';

import {
  DSInput,
  DSButton,
  DSLoadingSpinner,
  DSBanner,
} from '../../components';
import { BTN_TYPES } from '../../components/DSButton';
import { useState } from 'react';

const StyledWelcomeSection = styled.div`
  ${tw`
    flex flex-col
    items-center
  `};

  h3 {
    ${tw`
      font-medium
      text-2xl
      leading-[2.375rem]
      py-2.5
    `};
  }

  h5 {
    ${tw`
      font-normal
      text-sm text-center
      opacity-80
      sm:w-2/5
    `};
  }
`;

const StyledFormSection = styled.div`
  ${tw`
    flex flex-1
    justify-center
  `};
`;

const StyledFormContainer = styled.div`
  ${tw`
    flex flex-col
    w-[21.25rem]
    items-end
    gap-4
    text-sm
  `};
`;

const headingCopy = 'Enter your verification code';
const buttonLabelCopy = 'Confirm';
const signinLinkCopy = 'Sent to the wrong email? Go back to sign in';
const formId = 'verify-account-form';

const VerifyAccountPage = () => {
  const navigation = useNavigation();
  const [searchParams] = useSearchParams();
  const fetcherData = useActionData();

  const [verificationCodeHasValue, setVerificationCodeHasValue] =
    useState(false);

  const buttonDisabled =
    !verificationCodeHasValue || navigation.state !== 'idle';
  const hasError =
    navigation.state === 'idle' && fetcherData?.formError?.length > 0;

  return (
    <div tw="flex flex-col gap-7.5">
      <DSBanner
        body={fetcherData?.formError || ''}
        variant="error"
        isVisible={hasError}
      />
      <StyledWelcomeSection>
        <h3>{headingCopy}</h3>
        <h5>{`We have sent a password reset code to ${searchParams.get('email') ?? ''}. Please enter the code you received below:`}</h5>
      </StyledWelcomeSection>
      <StyledFormSection>
        <Form key="auth-verify-account" id={formId} method="post">
          <StyledFormContainer>
            <input
              type="hidden"
              name="email"
              value={searchParams.get('email')}
            />
            <input
              type="hidden"
              name="source"
              value={searchParams.get('from')}
            />
            <div tw="w-full">
              <DSInput
                dark
                defaultValue={fetcherData?.fields?.code}
                placeholder="Enter verification code"
                type="text"
                name="code"
                onChange={(e) =>
                  setVerificationCodeHasValue(e.target.value.length > 0)
                }
                error={!!fetcherData?.fieldErrors?.code}
              />
            </div>
            <DSButton
              form={formId}
              variant={
                !buttonDisabled ? BTN_TYPES.primary : BTN_TYPES.secondary
              }
              label={buttonLabelCopy}
              disabled={buttonDisabled}
              fullWidth
              dark
              icon={
                navigation.state !== 'idle' ? <DSLoadingSpinner /> : undefined
              }
              iconAutoWidth
            />
          </StyledFormContainer>
        </Form>
      </StyledFormSection>
      <div tw="flex justify-center text-sm">
        <Link to="/signin">{signinLinkCopy}</Link>
      </div>
    </div>
  );
};

export default VerifyAccountPage;
