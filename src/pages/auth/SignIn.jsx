import { useState } from 'react';
import styled from 'styled-components';
import tw from 'twin.macro';
import {
  Link,
  useActionData,
  useNavigation,
  useLoaderData,
  Form,
} from 'react-router-dom';

import {
  DSInput,
  DSButton,
  DSLoadingSpinner,
  DSBanner,
} from '../../components';
import { EyeOff, EyeOn } from '../../components/svg/icons';
import { BTN_TYPES } from '../../components/DSButton';

const StyledWelcomeSection = styled.div`
  ${tw`
    flex flex-col
    items-center
    px-3
    text-center
    
  `};

  h3 {
    ${tw`
      font-medium
      text-xl
      sm:text-2xl
      leading-[2.375rem]
      py-2.5
    `};
  }

  h5 {
    ${tw`
      font-normal
      text-sm
      opacity-80
    `};
  }
`;

const StyledFormSection = styled.div`
  ${tw`
    flex flex-1
    justify-center
    px-5
  `};
`;

const StyledFormContainer = styled.div`
  ${tw`
    flex flex-col
    w-[21.25rem]
    max-w-[100vw]
    items-end
    gap-4
    text-sm
    px-5
  `};
`;

const StyledMainContainer = styled.div`
  ${tw`
      flex flex-col
      gap-7.5
      max-w-[100vw]
    `};
  .dsl-create-account-link {
    ${tw`
          flex
          justify-center
          text-sm
      `};
    a {
      ${tw`
          max-w-[25ch]
          md:max-w-full
          text-center
        `}
    }
  }
`;

const headingCopy = 'Sign in to Mobile Resource Manager';
const subheadingCopy = 'Please enter your details.';
const buttonLabelCopy = 'Sign in';
const signupLinkCopy = 'Received your invite? Create your account here';
const resetPasswordSuccessCopy =
  "You've successfully reset your password. You may sign in using your new password.";
const errorBannerCopy =
  "We couldn't find an account matching the username and password you entered. Please check your username and password and try again.";
const formId = 'signin-form';

const DSSignIn = () => {
  const navigation = useNavigation();
  const fetcherData = useActionData();
  const loaderData = useLoaderData();
  const [isHidden, setIsHidden] = useState(true);
  const [emailHasValue, setEmailHasValue] = useState(false);
  const [passwordHasValue, setPasswordHasValue] = useState(false);

  const buttonDisabled =
    !emailHasValue || !passwordHasValue || navigation.state !== 'idle';
  const hasError = !!fetcherData?.formError && navigation.state === 'idle';
  const isResetPasswordSuccess =
    navigation.state === 'idle' &&
    loaderData?.resetPasswordStatus === 'success' &&
    !hasError;

  return (
    <StyledMainContainer>
      <DSBanner
        body={resetPasswordSuccessCopy}
        variant="info"
        isVisible={isResetPasswordSuccess}
      />
      <DSBanner variant="error" body={errorBannerCopy} isVisible={hasError} />
      <StyledWelcomeSection>
        <h3>{headingCopy}</h3>
        <h5>{subheadingCopy}</h5>
      </StyledWelcomeSection>
      <StyledFormSection>
        <Form key="auth-signin" id={formId} method="post">
          <StyledFormContainer>
            <div tw="w-full">
              <DSInput
                dark
                defaultValue={fetcherData?.fields?.email}
                placeholder="Enter your email"
                type="email"
                name="email"
                onChange={(e) => setEmailHasValue(e.target.value.length > 0)}
                error={hasError}
              />
            </div>
            <div tw="w-full">
              <DSInput
                dark
                defaultValue={fetcherData?.fields?.password}
                placeholder="Enter your password"
                type={isHidden ? 'password' : 'text'}
                name="password"
                onChange={(e) => setPasswordHasValue(e.target.value.length > 0)}
                endIconOnClick={() => setIsHidden(!isHidden)}
                endIconElement={isHidden ? EyeOff : EyeOn}
                error={hasError}
              />
            </div>
            <div tw="text-sm text-end">
              <Link to="/forgot-password">Forgot your password?</Link>
            </div>
            <DSButton
              form={formId}
              variant={!buttonDisabled ? BTN_TYPES.primary : BTN_TYPES.secondary}
              label={buttonLabelCopy}
              disabled={buttonDisabled}
              fullWidth
              dark
              icon={
                navigation.state !== 'idle' ? <DSLoadingSpinner /> : undefined
              }
              iconAutoWidth
            />
          </StyledFormContainer>
        </Form>
      </StyledFormSection>

      <div className="dsl-create-account-link">
        <Link to="/create-account">{signupLinkCopy}</Link>
      </div>
    </StyledMainContainer>
  );
};

export default DSSignIn;
