import { useState } from 'react';
import styled from 'styled-components';
import tw from 'twin.macro';
import { Link, useActionData, useNavigation, Form } from 'react-router-dom';

import {
  DSInput,
  DSButton,
  DSLoadingSpinner,
  DSBanner,
} from '../../components';
import { EyeOff, EyeOn } from '../../components/svg/icons';
import { BTN_TYPES } from '../../components/DSButton';

const StyledWelcomeSection = styled.div`
  ${tw`
    flex flex-col
    items-center
    px-3
    text-center
  `};

  h3 {
    ${tw`
      font-medium
      text-xl
      sm:text-2xl
      leading-[2.375rem]
      py-2.5
    `};
  }

  h5 {
    ${tw`
      font-normal
      text-sm
      opacity-80
    `};
  }
`;

const StyledFormSection = styled.div`
  ${tw`
    flex flex-1
    justify-center
    px-5
  `};
`;

const StyledFormContainer = styled.div`
  ${tw`
    flex flex-col
    w-[21.25rem]
    max-w-[100vw]
    items-end
    gap-4
    text-sm
    px-5
  `};
`;

const StyledMainContainer = styled.div`
  ${tw`
      flex flex-col
      gap-7.5
      max-w-[100vw]
    `};
  .dsl-sign-in-link {
    ${tw`
          flex
          justify-center
          text-sm
      `};
  }
  .dsl-hint-area {
    ${tw`
        text-2xs
        leading-normal
        mt-0.5
        text-dslGray-3
        whitespace-pre-line
      `};
  }
`;

const hintCopy = `Password requirements:
  - Min length: 8 characters
  - Contain at least 1 number
  - Contain at least 1 symbol like "?$%^&).
  - Contain at least 1 uppercase letter
  - Contain at least 1 lowercase letter`;
const headingCopy = 'Sign up to Mobile Resource Manager';
const subheadingCopy = 'Please enter your details.';
const buttonLabelCopy = 'Create Account';
const signinLinkCopy = 'Back to sign in';
const formId = 'create-account-form';

const CreateAccountPage = () => {
  const navigation = useNavigation();
  const fetcherData = useActionData();

  const [emailHasValue, setEmailHasValue] = useState(false);
  const [firstNameHasValue, setFirstNameHasValue] = useState(false);
  const [lastNameHasValue, setLastNameHasValue] = useState(false);
  const [passwordHasValue, setPasswordHasValue] = useState(false);
  const [isHidden, setIsHidden] = useState(true);

  const buttonDisabled =
    !emailHasValue ||
    !firstNameHasValue ||
    !lastNameHasValue ||
    !passwordHasValue ||
    navigation.state !== 'idle';

  const hasError =
    navigation.state === 'idle' && fetcherData?.formError?.length > 0;

  return (
    <StyledMainContainer>
      <DSBanner
        body={fetcherData?.formError || ''}
        variant="error"
        isVisible={hasError}
      />
      <StyledWelcomeSection>
        <h3>{headingCopy}</h3>
        <h5>{subheadingCopy}</h5>
      </StyledWelcomeSection>
      <StyledFormSection>
        <Form key="auth-signup" id={formId} method="post">
          <StyledFormContainer>
            <div tw="w-full">
              <DSInput
                dark
                defaultValue={fetcherData?.fields?.email}
                placeholder="Enter your email"
                type="email"
                name="email"
                onChange={(e) => setEmailHasValue(e.target.value.length > 0)}
                error={!!fetcherData?.fieldErrors?.email}
              />
            </div>
            <div tw="w-full">
              <DSInput
                dark
                defaultValue={fetcherData?.fields?.firstName}
                placeholder="Enter your first name"
                type="text"
                name="firstName"
                onChange={(e) => setFirstNameHasValue(e.target.value.length > 0)}
                error={!!fetcherData?.fieldErrors?.firstName}
              />
            </div>
            <div tw="w-full">
              <DSInput
                dark
                defaultValue={fetcherData?.fields?.lastName}
                placeholder="Enter your last name"
                type="text"
                name="lastName"
                onChange={(e) => setLastNameHasValue(e.target.value.length > 0)}
                error={!!fetcherData?.fieldErrors?.lastName}
              />
            </div>
            <div tw="w-full">
              <DSInput
                dark
                defaultValue={fetcherData?.fields?.password}
                placeholder="Enter your password"
                type={isHidden ? 'password' : 'text'}
                name="password"
                onChange={(e) => setPasswordHasValue(e.target.value.length > 0)}
                error={!!fetcherData?.fieldErrors?.password}
                endIconOnClick={() => setIsHidden(!isHidden)}
                endIconElement={isHidden ? EyeOff : EyeOn}
              />
              <div className="dsl-hint-area">{hintCopy}</div>
            </div>
            <DSButton
              form={formId}
              variant={
                !buttonDisabled ? BTN_TYPES.primary : BTN_TYPES.secondary
              }
              label={buttonLabelCopy}
              disabled={buttonDisabled}
              fullWidth
              dark
              icon={
                navigation.state !== 'idle' ? <DSLoadingSpinner /> : undefined
              }
              iconAutoWidth
            />
          </StyledFormContainer>
        </Form>
      </StyledFormSection>
      <div className="dsl-sign-in-link">
        <Link to="/signin">{signinLinkCopy}</Link>
      </div>
    </StyledMainContainer>
  );
};

export default CreateAccountPage;
