import styled from 'styled-components';
import { useState } from 'react';
import tw from 'twin.macro';
import { Link, useActionData, useNavigation, Form } from 'react-router-dom';

import {
  DSInput,
  DSButton,
  DSLoadingSpinner,
  DSBanner,
} from '../../components';
import { BTN_TYPES } from '../../components/DSButton';

const StyledWelcomeSection = styled.div`
  ${tw`
    flex flex-col
    items-center
    px-3
    text-center
  `};

  h3 {
    ${tw`
      font-medium
      text-2xl
      leading-[2.375rem]
      py-2.5
    `};
  }

  h5 {
    ${tw`
      font-normal
      text-sm text-center
      opacity-80
      whitespace-pre-line
    `};
  }
`;

const StyledFormSection = styled.div`
  ${tw`
    flex flex-1
    justify-center
    px-3
  `};
`;

const StyledFormContainer = styled.div`
  ${tw`
    flex flex-col
    w-[21.25rem]
    max-w-[18.75rem]
    items-end
    gap-4
    text-sm
  `};
  .dsl-form-inputs {
    ${tw`
      w-full
      flex flex-col
      gap-3
    `}
  }
`;

const StyledFooter = styled.div`
  ${tw`
    flex justify-center
    text-sm
    mt-4
  `};
`;

const StyledMainContainer = styled.div`
  ${tw`
      flex flex-col
      gap-6
      px-3
      max-w-[100vw]
    `};
`;

const headingCopy = 'Forgot Password?';
const subheadingCopy =
  "Please enter the email address \nyou used when you joined and we'll send you \ninstructions to reset your password.";
const buttonLabelCopy = 'Send Instructions';
const signinLinkCopy = 'Back to sign in';
const formId = 'forgot-password-form';

const ForgotPasswordPage = () => {
  const navigation = useNavigation();
  const fetcherData = useActionData();
  const [emailHasValue, setEmailHasValue] = useState(false);

  const buttonDisabled = !emailHasValue || navigation.state !== 'idle';
  const hasError =
    navigation.state === 'idle' && fetcherData?.formError?.length > 0;

  return (
    <StyledMainContainer>
      <DSBanner
        body={fetcherData?.formError || ''}
        variant="error"
        isVisible={hasError}
      />
      <StyledWelcomeSection>
        <h3>{headingCopy}</h3>
        <h5>{subheadingCopy}</h5>
      </StyledWelcomeSection>
      <StyledFormSection>
        <Form key="auth-forgot-password" id={formId} method="post">
          <StyledFormContainer>
            <div className="dsl-form-inputs">
              <DSInput
                dark
                defaultValue={fetcherData?.fields?.email}
                error={!!fetcherData?.fieldErrors?.email}
                onChange={(e) => setEmailHasValue(e.target.value.length > 0)}
                placeholder="Enter your email"
                type="email"
                name="email"
              />
              <DSButton
                form={formId}
                variant={
                  !buttonDisabled ? BTN_TYPES.primary : BTN_TYPES.secondary
                }
                label={buttonLabelCopy}
                disabled={buttonDisabled}
                fullWidth
                dark
                icon={
                  navigation.state !== 'idle' ? <DSLoadingSpinner /> : undefined
                }
                iconAutoWidth
              />
            </div>
          </StyledFormContainer>
        </Form>
      </StyledFormSection>
      <StyledFooter>
        <Link to="/signin">{signinLinkCopy}</Link>
      </StyledFooter>
    </StyledMainContainer>
  );
};

export default ForgotPasswordPage;
