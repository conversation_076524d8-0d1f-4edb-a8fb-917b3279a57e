import { useCallback, useEffect, useReducer, useState } from 'react';
import {
  redirect,
  json,
  useOutletContext,
  useAsyncValue,
} from 'react-router-dom';
import tw from 'twin.macro';
import styled from 'styled-components';
import { DateTime } from 'luxon';

import { logout } from '../utils/session';
import {
  DSContentPageHeader,
  DSButton,
  DSToastMessage,
  DSSchedule,
  DSSchedulePopover,
} from '../components';
import ToastMessageContextProvider from '../contexts/ToastMessageContext';
import { Add } from '../components/svg/icons';
import API from '../utils/api';

const {
  getSiteGroupLocations,
  getMobileSiteSchedules,
  getMobileSiteBlackouts,
} = API;

const ActionTypes = {
  SITE_GROUP_LOCATION_LIST: 'SITE_GROUP_LOCATION_LIST',
  MOBILE_SITE_SCHEDULE_LIST: 'MOBILE_SITE_SCHEDULE_LIST',
  MOBILE_SITE_BLACKOUT_LIST: 'MOBILE_SITE_BLACKOUT_LIST',
};

const initialState = {
  siteGroupLocations: [],
  mobileSiteSchedules: [],
  mobileSiteBlackouts: [],
};

const reducer = (state, action) => {
  switch (action.type) {
    case ActionTypes.SITE_GROUP_LOCATION_LIST: {
      const { siteGroupLocations } = action.payload;
      return {
        ...state,
        siteGroupLocations: siteGroupLocations ?? [],
      };
    }
    case ActionTypes.MOBILE_SITE_SCHEDULE_LIST: {
      const { mobileSiteSchedules } = action.payload;
      return {
        ...state,
        mobileSiteSchedules: mobileSiteSchedules ?? [],
      };
    }
    case ActionTypes.MOBILE_SITE_BLACKOUT_LIST: {
      const { mobileSiteBlackouts } = action.payload;
      return {
        ...state,
        mobileSiteBlackouts: mobileSiteBlackouts ?? [],
      };
    }
    default:
      return state;
  }
};

const StyledDashboardContainer = styled.div`
  ${tw`
    flex flex-col
    min-h-full
    w-full
  `};

  .toast-alert-cont {
    ${tw`
      fixed 
      bottom-[50px]
      z-[100]
      left-1/3  
    `}
  }
`;

export const dashboardAction = async ({ request }) => {
  const formData = await request.formData();
  const intent = formData.get('intent');

  switch (intent) {
    case 'get':
      return redirect('/app/profile');
    case 'delete':
      return logout();
    default:
      break;
  }
  return json({});
};

const Dashboard = () => {
  const { userData, onRefresh } = useOutletContext();
  const { allSites, mobileSites } = useAsyncValue();

  const siteInfo = {
    site_id: userData?.roles[0]?.site_id,
    tz: allSites[0]?.tz,
  };

  const [state, dispatch] = useReducer(reducer, initialState);

  const [openSchedulePopover, setOpenSchedulePopover] = useState(false);
  const [showCreateScheduleSuccessToast, setShowCreateScheduleSuccessToast] =
    useState(false);
  const [toastVariant, setToastVariant] = useState('');
  const [toastTitle, setToastTitle] = useState('');
  const [toastDescription, setToastDescription] = useState('');
  const [globalDate, setGlobalDate] = useState(
    DateTime.now().setZone(siteInfo?.tz).toISO(),
  );

  // Charles Proxy API testing
  const [apiTestResults, setApiTestResults] = useState([]);

  const testApiCall = async (endpoint, description) => {
    const timestamp = new Date().toLocaleTimeString();
    try {
      console.log(`🔍 Testing API for Charles Proxy: ${description}`);
      const response = await fetch(`/api/${endpoint}`);
      const data = await response.json();
      setApiTestResults(prev => [...prev, {
        timestamp,
        endpoint,
        description,
        status: response.status,
        success: response.ok,
        data: JSON.stringify(data, null, 2)
      }]);
    } catch (error) {
      setApiTestResults(prev => [...prev, {
        timestamp,
        endpoint,
        description,
        status: 'Error',
        success: false,
        data: error.message
      }]);
    }
  };

  const fetchSiteGroupLocations = useCallback(async () => {
    try {
      const siteGroupLocations = await getSiteGroupLocations(siteInfo?.site_id);
      dispatch({
        type: ActionTypes.SITE_GROUP_LOCATION_LIST,
        payload: { siteGroupLocations },
      });
    } catch (error) {
      console.error('Error fetching site group locations:', error);
    }
  }, []);

  const fetchMobileSiteSchedules = useCallback(async () => {
    try {
      const scheduleResults = await Promise.all(
        mobileSites.map(async (mobileSite) => {
          const schedules = await getMobileSiteSchedules(
            siteInfo?.site_id,
            mobileSite.site_id,
          );
          return { [mobileSite.site_id]: schedules };
        }),
      );

      const mobileSiteSchedules = scheduleResults.reduce((acc, curr) => {
        return { ...acc, ...curr };
      }, {});

      dispatch({
        type: ActionTypes.MOBILE_SITE_SCHEDULE_LIST,
        payload: { mobileSiteSchedules },
      });
    } catch (error) {
      console.error('Error fetching mobile site schedules:', error);
    }
  }, []);

  const fetchMobileSiteBlackouts = useCallback(async () => {
    try {
      const blackoutResults = await Promise.all(
        mobileSites.map(async (mobileSite) => {
          const blackouts = await getMobileSiteBlackouts(
            siteInfo?.site_id,
            mobileSite?.site_id,
          );
          return { [mobileSite?.site_id]: blackouts };
        }),
      );

      const mobileSiteBlackouts = blackoutResults.reduce((acc, curr) => {
        return { ...acc, ...curr };
      }, {});

      dispatch({
        type: ActionTypes.MOBILE_SITE_BLACKOUT_LIST,
        payload: { mobileSiteBlackouts },
      });
    } catch (error) {
      console.error('Error fetching mobile site blackouts:', error);
    }
  }, []);

  useEffect(() => {
    if (siteInfo?.site_id) {
      fetchSiteGroupLocations();
      fetchMobileSiteSchedules();
      fetchMobileSiteBlackouts();
    }
  }, [
    fetchSiteGroupLocations,
    fetchMobileSiteSchedules,
    fetchMobileSiteBlackouts,
  ]);

  const handleRefreshPage = () => {
    onRefresh();
  };

  const handleCreateSchedule = () => {
    setOpenSchedulePopover(true);
  };

  const handleOnClosePopover = (data) => {
    setOpenSchedulePopover(false);
    if (data) {
      setToastVariant(data?.variant);
      setToastTitle(data?.title);
      setToastDescription(data?.description);
      setShowCreateScheduleSuccessToast(true);
      setTimeout(() => {
        handleRefreshPage();
      }, 2000);
      setTimeout(() => {
        resetToast();
      }, 4000);
    }
  };

  const handleToastDismiss = () => {
    resetToast();
  };

  const resetToast = () => {
    setShowCreateScheduleSuccessToast(false);
    setToastVariant('');
    setToastTitle('');
    setToastDescription('');
  };

  const handleOnDeleteSession = (data) => {
    if (data) {
      setToastVariant(data?.variant);
      setToastTitle(data?.title);
      setToastDescription(data?.description);
      setShowCreateScheduleSuccessToast(true);
      setTimeout(() => {
        resetToast();
      }, 4000);
    }
  };

  const buttons = [
    <DSButton
      key="add"
      variant="primary"
      icon={<Add />}
      label=" Schedule"
      onClick={handleCreateSchedule}
      hug
    />,
  ];

  return (
    <StyledDashboardContainer>
      <DSContentPageHeader
        title={`Mobile Resource Schedule`}
        buttons={buttons}
        onRefresh={handleRefreshPage}
      />

      {/* Charles Proxy API Testing Section */}
      <div tw="mb-6 p-4 border rounded-lg bg-blue-50">
        <h2 tw="text-lg font-semibold mb-2">🔍 Charles Proxy API Testing</h2>
        <p tw="text-sm text-gray-600 mb-3">
          Generate API traffic for Charles Proxy inspection. Check Charles for request/response details.
        </p>

        <div tw="flex flex-wrap gap-2 mb-3">
          <button
            onClick={() => testApiCall('mrm-access/profile', 'User Profile')}
            tw="px-3 py-1 text-xs bg-blue-500 text-white rounded hover:bg-blue-600"
          >
            👤 Profile
          </button>
          <button
            onClick={() => testApiCall('mrm-access/sites', 'All Sites')}
            tw="px-3 py-1 text-xs bg-green-500 text-white rounded hover:bg-green-600"
          >
            🏢 Sites
          </button>
          <button
            onClick={() => testApiCall(`mrm/site/${siteInfo?.site_id}/info`, 'Site Info')}
            tw="px-3 py-1 text-xs bg-purple-500 text-white rounded hover:bg-purple-600"
          >
            📊 Site Info
          </button>
          <button
            onClick={() => setApiTestResults([])}
            tw="px-3 py-1 text-xs bg-gray-500 text-white rounded hover:bg-gray-600"
          >
            🗑️ Clear
          </button>
        </div>

        {apiTestResults.length > 0 && (
          <div tw="max-h-32 overflow-y-auto">
            <div tw="text-xs space-y-1">
              {apiTestResults.slice(-3).map((result, index) => (
                <div
                  key={index}
                  tw="p-2 rounded"
                  style={{ backgroundColor: result.success ? '#dcfce7' : '#fecaca' }}
                >
                  [{result.timestamp}] {result.description} - {result.status}
                </div>
              ))}
            </div>
          </div>
        )}
      </div>

      <ToastMessageContextProvider>
        <DSSchedulePopover
          showModal={openSchedulePopover}
          onClose={(data) => handleOnClosePopover(data)}
        />
      </ToastMessageContextProvider>
      <DSSchedule
        siteInfo={siteInfo}
        globalDate={globalDate}
        setGlobalDate={setGlobalDate}
        siteLocations={state.siteGroupLocations}
        mobileResources={mobileSites}
        mobileSiteSchedules={state.mobileSiteSchedules}
        onDelete={(data) => handleOnDeleteSession(data)}
      />
      {showCreateScheduleSuccessToast ? (
        <div className="toast-alert-cont">
          <DSToastMessage
            title={toastTitle}
            description={toastDescription}
            variant={toastVariant}
            onDismiss={handleToastDismiss}
          />
        </div>
      ) : null}
    </StyledDashboardContainer>
  );
};

export default Dashboard;
