import { useCallback, useEffect, useReducer, useState } from 'react';
import {
  redirect,
  json,
  useOutletContext,
  useAsyncValue,
} from 'react-router-dom';
import tw from 'twin.macro';
import styled from 'styled-components';
import { DateTime } from 'luxon';

import { logout } from '../utils/session';
import {
  DSContentPageHeader,
  DSButton,
  DSToastMessage,
  DSSchedule,
  DSSchedulePopover,
} from '../components';
import ToastMessageContextProvider from '../contexts/ToastMessageContext';
import { Add } from '../components/svg/icons';
import API from '../utils/api';

const {
  getSiteGroupLocations,
  getMobileSiteSchedules,
  getMobileSiteBlackouts,
} = API;

const ActionTypes = {
  SITE_GROUP_LOCATION_LIST: 'SITE_GROUP_LOCATION_LIST',
  MOBILE_SITE_SCHEDULE_LIST: 'MOBILE_SITE_SCHEDULE_LIST',
  MOBILE_SITE_BLACKOUT_LIST: 'MOBILE_SITE_BLACKOUT_LIST',
};

const initialState = {
  siteGroupLocations: [],
  mobileSiteSchedules: [],
  mobileSiteBlackouts: [],
};

const reducer = (state, action) => {
  switch (action.type) {
    case ActionTypes.SITE_GROUP_LOCATION_LIST: {
      const { siteGroupLocations } = action.payload;
      return {
        ...state,
        siteGroupLocations: siteGroupLocations ?? [],
      };
    }
    case ActionTypes.MOBILE_SITE_SCHEDULE_LIST: {
      const { mobileSiteSchedules } = action.payload;
      return {
        ...state,
        mobileSiteSchedules: mobileSiteSchedules ?? [],
      };
    }
    case ActionTypes.MOBILE_SITE_BLACKOUT_LIST: {
      const { mobileSiteBlackouts } = action.payload;
      return {
        ...state,
        mobileSiteBlackouts: mobileSiteBlackouts ?? [],
      };
    }
    default:
      return state;
  }
};

const StyledDashboardContainer = styled.div`
  ${tw`
    flex flex-col
    min-h-full
    w-full
  `};

  .toast-alert-cont {
    ${tw`
      fixed 
      bottom-[50px]
      z-[100]
      left-1/3  
    `}
  }
`;

export const dashboardAction = async ({ request }) => {
  const formData = await request.formData();
  const intent = formData.get('intent');

  switch (intent) {
    case 'get':
      return redirect('/app/profile');
    case 'delete':
      return logout();
    default:
      break;
  }
  return json({});
};

const Dashboard = () => {
  const { userData, onRefresh } = useOutletContext();
  const { allSites, mobileSites } = useAsyncValue();

  const siteInfo = {
    site_id: userData?.roles[0]?.site_id,
    tz: allSites[0]?.tz,
  };

  const [state, dispatch] = useReducer(reducer, initialState);

  const [openSchedulePopover, setOpenSchedulePopover] = useState(false);
  const [showCreateScheduleSuccessToast, setShowCreateScheduleSuccessToast] =
    useState(false);
  const [toastVariant, setToastVariant] = useState('');
  const [toastTitle, setToastTitle] = useState('');
  const [toastDescription, setToastDescription] = useState('');
  const [globalDate, setGlobalDate] = useState(
    DateTime.now().setZone(siteInfo?.tz).toISO(),
  );

  const fetchSiteGroupLocations = useCallback(async () => {
    try {
      const siteGroupLocations = await getSiteGroupLocations(siteInfo?.site_id);
      dispatch({
        type: ActionTypes.SITE_GROUP_LOCATION_LIST,
        payload: { siteGroupLocations },
      });
    } catch (error) {
      console.error('Error fetching site group locations:', error);
    }
  }, []);

  const fetchMobileSiteSchedules = useCallback(async () => {
    try {
      const scheduleResults = await Promise.all(
        mobileSites.map(async (mobileSite) => {
          const schedules = await getMobileSiteSchedules(
            siteInfo?.site_id,
            mobileSite.site_id,
          );
          return { [mobileSite.site_id]: schedules };
        }),
      );

      const mobileSiteSchedules = scheduleResults.reduce((acc, curr) => {
        return { ...acc, ...curr };
      }, {});

      dispatch({
        type: ActionTypes.MOBILE_SITE_SCHEDULE_LIST,
        payload: { mobileSiteSchedules },
      });
    } catch (error) {
      console.error('Error fetching mobile site schedules:', error);
    }
  }, []);

  const fetchMobileSiteBlackouts = useCallback(async () => {
    try {
      const blackoutResults = await Promise.all(
        mobileSites.map(async (mobileSite) => {
          const blackouts = await getMobileSiteBlackouts(
            siteInfo?.site_id,
            mobileSite?.site_id,
          );
          return { [mobileSite?.site_id]: blackouts };
        }),
      );

      const mobileSiteBlackouts = blackoutResults.reduce((acc, curr) => {
        return { ...acc, ...curr };
      }, {});

      dispatch({
        type: ActionTypes.MOBILE_SITE_BLACKOUT_LIST,
        payload: { mobileSiteBlackouts },
      });
    } catch (error) {
      console.error('Error fetching mobile site blackouts:', error);
    }
  }, []);

  useEffect(() => {
    if (siteInfo?.site_id) {
      fetchSiteGroupLocations();
      fetchMobileSiteSchedules();
      fetchMobileSiteBlackouts();
    }
  }, [
    fetchSiteGroupLocations,
    fetchMobileSiteSchedules,
    fetchMobileSiteBlackouts,
  ]);

  const handleRefreshPage = () => {
    onRefresh();
  };

  const handleCreateSchedule = () => {
    setOpenSchedulePopover(true);
  };

  const handleOnClosePopover = (data) => {
    setOpenSchedulePopover(false);
    if (data) {
      setToastVariant(data?.variant);
      setToastTitle(data?.title);
      setToastDescription(data?.description);
      setShowCreateScheduleSuccessToast(true);
      setTimeout(() => {
        handleRefreshPage();
      }, 2000);
      setTimeout(() => {
        resetToast();
      }, 4000);
    }
  };

  const handleToastDismiss = () => {
    resetToast();
  };

  const resetToast = () => {
    setShowCreateScheduleSuccessToast(false);
    setToastVariant('');
    setToastTitle('');
    setToastDescription('');
  };

  const handleOnDeleteSession = (data) => {
    if (data) {
      setToastVariant(data?.variant);
      setToastTitle(data?.title);
      setToastDescription(data?.description);
      setShowCreateScheduleSuccessToast(true);
      setTimeout(() => {
        resetToast();
      }, 4000);
    }
  };

  const buttons = [
    <DSButton
      key="add"
      variant="primary"
      icon={<Add />}
      label=" Schedule"
      onClick={handleCreateSchedule}
      hug
    />,
  ];

  return (
    <StyledDashboardContainer>
      <DSContentPageHeader
        title={`Mobile Resource Schedule`}
        buttons={buttons}
        onRefresh={handleRefreshPage}
      />
      <ToastMessageContextProvider>
        <DSSchedulePopover
          showModal={openSchedulePopover}
          onClose={(data) => handleOnClosePopover(data)}
        />
      </ToastMessageContextProvider>
      <DSSchedule
        siteInfo={siteInfo}
        globalDate={globalDate}
        setGlobalDate={setGlobalDate}
        siteLocations={state.siteGroupLocations}
        mobileResources={mobileSites}
        mobileSiteSchedules={state.mobileSiteSchedules}
        onDelete={(data) => handleOnDeleteSession(data)}
      />
      {showCreateScheduleSuccessToast ? (
        <div className="toast-alert-cont">
          <DSToastMessage
            title={toastTitle}
            description={toastDescription}
            variant={toastVariant}
            onDismiss={handleToastDismiss}
          />
        </div>
      ) : null}
    </StyledDashboardContainer>
  );
};

export default Dashboard;
