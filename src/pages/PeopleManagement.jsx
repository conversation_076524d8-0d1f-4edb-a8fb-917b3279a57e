import { useState, useEffect, useReducer } from 'react';
import styled from 'styled-components';
import tw from 'twin.macro';
import { useRevalidator, useOutletContext } from 'react-router';

import API from '../utils/api';
import { USER_ROLES, USER_ROLE_DISPLAY } from '../utils/constants';
import { validateEmail } from '../utils/validate.mjs';
import { sortFirstName, sortLastName } from '../utils/common';

import { Add, Search, Trash, Pencil } from '../components/svg/icons';
import {
  DSButton,
  DSContentPageHeader,
  DSDataTable,
  DSIconButton,
  DSInput,
  DSUserDetails,
  DSLoadingSpinner,
  DSAlertPrompt,
} from '../components';

const {
  getUserlist,
  getSuperAdminUserlist,
  addNewUser: apiAddNewUser,
  updateUserRole: apiUpdateUserRole,
  resendInvitation: apiResendInvitation,
  deleteUserRole,
  addUpdateSuperAdmin,
  deleteSuperAdmin,
} = API;

const users = [];

const StyledPeopleManagementPage = styled.div`
  ${tw`
    flex flex-col
    min-h-full
    w-full
  `};

  .search-cont {
    ${tw`
      flex
      self-end
      px-10
      py-7.5
      w-[335px]
    `}
  }
`;

const StyledNameCell = styled.div`
  ${tw`text-dslBlack-2 pointer-events-none`}
`;

const StyledEmailCell = styled.div`
  ${tw`text-dslGray-4 pointer-events-none`}
`;

const ActionTypes = {
  FETCH_USERS: 'FETCH_USERS',
};

const reducer = (state, action) => {
  switch (action.type) {
    case ActionTypes.FETCH_USERS: {
      const { users } = action.payload;

      if (!users?.length) {
        return state;
      }

      return {
        ...state,
        users,
      };
    }

    default:
      return state;
  }
};

const dataTableColumns = ({
  editUser,
  currentUser,
  handleConfirmDeleteUser,
}) => {
  return [
    {
      name: 'First Name',
      cell: (row) => <StyledNameCell>{row.first_name ?? '-'}</StyledNameCell>,
      sortable: true,
      sortFunction: sortFirstName,
    },
    {
      name: 'Last Name',
      cell: (row) => <StyledNameCell>{row.last_name ?? '-'}</StyledNameCell>,
      sortable: true,
      sortFunction: sortLastName,
    },
    {
      name: 'Role',
      selector: (row) =>
        USER_ROLE_DISPLAY[
          row.mrm_role || (row.super_admin ? USER_ROLES.SUPER_ADMIN : '')
        ],
      cell: (row) => (
        <StyledNameCell>
          {
            USER_ROLE_DISPLAY[
              row.mrm_role || (row.super_admin ? USER_ROLES.SUPER_ADMIN : '')
            ]
          }
        </StyledNameCell>
      ),
      sortable: true,
    },
    {
      name: 'Email',
      selector: (row) => row.user_email,
      cell: (row) => <StyledEmailCell>{row.user_email}</StyledEmailCell>,
      sortable: true,
    },
    {
      name: 'Date Added',
      width: '120px',
      cell: (row) => {
        return row.created_at
          ? new Date(row.created_at).toLocaleDateString()
          : '-';
      },
    },
    {
      name: 'Status',
      cell: (row) => {
        return '-';
      },
      width: '82px',
    },
    {
      cell: (row) => (
        <div tw="flex flex-row gap-2.5">
          <DSIconButton
            icon={Pencil}
            onClick={() => editUser(row)}
            disabled={row.user_email === currentUser.email}
          />
          <DSIconButton
            icon={Trash}
            onClick={() => handleConfirmDeleteUser(row)}
            disabled={row.user_email === currentUser.email}
          />
        </div>
      ),
      width: '86px',
    },
  ];
};

const pageTitle = 'People Management';

const PeopleManagementPage = () => {
  const revalidator = useRevalidator();
  const { userData: currentUser, onRefresh } = useOutletContext();
  const [state, dispatch] = useReducer(reducer, {});
  const [searchTerm, setSearchTerm] = useState('');
  const [showUserDetails, setShowUserDetails] = useState(false);
  const [selectedUser, setSelectedUser] = useState();
  const [newEmail, setNewEmail] = useState();
  const [emailError, setEmailError] = useState();
  const [userRole, setUserRole] = useState();
  const [savingUser, setSavingUser] = useState(false);
  const [deletingUser, setDeletingUser] = useState(false);
  const [loadingUsers, setLoadingUsers] = useState(false);
  const [showConfirmDelete, setShowConfirmDelete] = useState(false);

  const dropdownOptions = Object.values(USER_ROLES).map((role) => {
    return {
      id: role,
      label: USER_ROLE_DISPLAY[role],
    };
  });

  useEffect(() => {
    const fetchUsers = async () => {
      if (revalidator.state === 'idle') {
        setLoadingUsers(true);
        try {
          const [users, superAdmin] = await Promise.all([
            getUserlist(currentUser?.roles?.[0]?.site_id),
            getSuperAdminUserlist(),
          ]);

          // Merge both lists users and super admin list
          const combinedUsers = [...users, ...superAdmin];

          // Sort by user_email in ascending order
          combinedUsers.sort((a, b) => {
            const emailA = a.user_email?.toLowerCase() || '';
            const emailB = b.user_email?.toLowerCase() || '';
            return emailA.localeCompare(emailB);
          });

          dispatch({
            type: ActionTypes.FETCH_USERS,
            payload: { users: combinedUsers },
          });
        } catch (error) {
          console.error('Error fetching users or super admins:', error);
          // Optional: setError('Failed to load users');
        } finally {
          setLoadingUsers(false);
        }
      }
    };

    fetchUsers();
  }, [revalidator.state]);

  const onChangeSearch = (e) => {
    setSearchTerm(e.target.value);
  };

  const onClickUserRow = (user) => {
    if (user.user_email === currentUser.email) {
      return;
    }
    setSelectedUser(user);
    setUserRole(
      dropdownOptions.find((role) =>
        user?.super_admin
          ? role.id === 'super_admin'
          : role.id === user?.mrm_role,
      ),
    );
    setShowUserDetails(true);
  };

  const onCloseUserDetails = () => {
    setSelectedUser(null);
    setUserRole(null);
    setNewEmail('');
    setShowUserDetails(false);
  };

  const handleConfirmDeleteUser = (user) => {
    setSelectedUser(user);
    setUserRole(
      dropdownOptions.find((role) =>
        user?.super_admin
          ? role.id === 'super_admin'
          : role.id === user?.mrm_role,
      ),
    );
    setShowConfirmDelete(true);
  };

  const onChangeEmail = (email) => {
    setEmailError(null);
    setNewEmail(email);
  };

  const addNewUser = async () => {
    const user = {
      user_email: newEmail,
      mrm_role: userRole.id,
    };

    const emailValidationError = validateEmail(newEmail);
    if (emailValidationError) {
      setEmailError(emailValidationError);
      return;
    }

    try {
      setSavingUser(true);
      if (userRole.id === USER_ROLES.SUPER_ADMIN) {
        const newUser = await addUpdateSuperAdmin(user);
      } else {
        const newUser = await apiAddNewUser(
          user,
          currentUser?.roles?.[0].site_id,
        );
      }
      setShowUserDetails(false);
      onCloseUserDetails();
      revalidator.revalidate();
    } catch (e) {
      console.log('Could not create user: ', e);
    } finally {
      setSavingUser(false);
    }
  };

  const resendInvitation = async () => {
    console.log(selectedUser);
    try {
      const newUser = await apiResendInvitation(
        currentUser?.roles?.[0].site_id,
        selectedUser.user_email,
      );
    } catch (error) {}
  };

  const updateUserRole = async () => {
    try {
      setSavingUser(true);
      if (userRole.id === USER_ROLES.SUPER_ADMIN) {
        const user = {
          user_email: selectedUser.user_email,
        };
        const newUser = await addUpdateSuperAdmin(user);
      } else {
        await apiUpdateUserRole(
          selectedUser.user_site_roles_id,
          userRole.id,
          currentUser?.roles?.[0].site_id,
        );
      }
      setShowUserDetails(false);
      onCloseUserDetails();
      revalidator.revalidate();
    } catch (e) {
      console.log('Could not save user: ', e);
    } finally {
      setSavingUser(false);
    }
  };

  const deleteUser = async () => {
    try {
      setDeletingUser(true);
      console.log(userRole);
      if (userRole.id === USER_ROLES.SUPER_ADMIN) {
        await deleteSuperAdmin(selectedUser.user_site_roles_id);
      } else {
        await deleteUserRole(
          selectedUser.user_site_roles_id,
          currentUser?.roles?.[0].site_id,
        );
      }
      revalidator.revalidate();
      setShowConfirmDelete(false);
      setShowUserDetails(false);
      setSelectedUser(null);
      setNewEmail(null);
    } catch (e) {
      console.log('Could not delete user: ', e);
    } finally {
      setDeletingUser(false);
    }
  };

  const filteredUsers = state?.users?.filter((user) => {
    const lowerCaseSearchTerm = searchTerm.toLowerCase();
    return (
      user?.first_name?.toLowerCase().includes(lowerCaseSearchTerm) ||
      user?.last_name?.toLowerCase().includes(lowerCaseSearchTerm) ||
      user?.user_email?.toLowerCase().includes(lowerCaseSearchTerm)
    );
  });

  const LoadingComponent = () => (
    <div tw="flex items-center justify-center w-full py-56">
      <DSLoadingSpinner />
    </div>
  );

  const buttons = [
    <DSButton
      key="add"
      variant="primary"
      icon={<Add />}
      label=" People"
      onClick={() => setShowUserDetails(true)}
      hug
    />,
  ];

  return (
    <StyledPeopleManagementPage>
      <DSContentPageHeader
        title={pageTitle}
        buttons={buttons}
        onRefresh={onRefresh}
      />
      <div className="search-cont">
        <DSInput
          startIconElement={Search}
          placeholder="Search people..."
          value={searchTerm}
          onChange={onChangeSearch}
        />
      </div>
      <DSDataTable
        columns={dataTableColumns({
          editUser: onClickUserRow,
          currentUser,
          handleConfirmDeleteUser,
        })}
        data={searchTerm ? filteredUsers : state?.users}
        keyField="user_id"
        onRowClicked={(row) => onClickUserRow(row)}
        progressPending={loadingUsers}
        progressComponent={<LoadingComponent />}
      />
      <DSUserDetails
        isSelfAccount={false}
        showModal={showUserDetails}
        onClose={onCloseUserDetails}
        user={selectedUser}
        newEmail={newEmail}
        userRole={userRole}
        onChangeUserRole={setUserRole}
        onChangeEmail={onChangeEmail}
        loading={savingUser}
        dropdownOptions={dropdownOptions}
        updateUserRole={newEmail ? addNewUser : updateUserRole}
        resendInvitation={resendInvitation}
        editDisabled={currentUser?.email === selectedUser?.user_email}
        confirmDeleteUser={handleConfirmDeleteUser}
        emailError={emailError}
      />
      {showConfirmDelete && (
        <DSAlertPrompt
          title="Confirm Removal"
          body={`This will remove ${selectedUser.user_email}${selectedUser.site_id ? ` from site group ${selectedUser.site_id}` : ''}.
          
          Are you sure you want to remove this user?`}
          onCancel={() => setShowConfirmDelete(false)}
          onConfirm={deleteUser}
          danger
          actionText="Remove"
          loading={deletingUser}
        />
      )}
    </StyledPeopleManagementPage>
  );
};

export default PeopleManagementPage;
