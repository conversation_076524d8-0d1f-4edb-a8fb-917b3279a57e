import { createContext, useContext, useReducer } from 'react';

const SiteSelectorContext = createContext(null);
const siteSelectorDispatchContext = createContext(null);

export const useSiteSelector = () => {
  return useContext(SiteSelectorContext);
};

export const useSiteSelectorDispatch = () => {
  return useContext(siteSelectorDispatchContext);
};

export const SITE_SELECTOR_ACTIONS = {
  SET_SELECTED_SITE_GROUP: 'SET_SELECTED_SITE_GROUP',
};

const siteSelectorReducer = (state, action) => {
  switch (action.type) {
    case SITE_SELECTOR_ACTIONS.SET_SELECTED_SITE_GROUP:
      return { ...state, selectedSiteGroup: action.payload };
  }
};

export const SiteSelectorProvider = ({ children }) => {
  const [state, dispatch] = useReducer(siteSelectorReducer, initialState);

  return (
    <SiteSelectorContext.Provider value={state}>
      <siteSelectorDispatchContext.Provider value={dispatch}>
        {children}
      </siteSelectorDispatchContext.Provider>
    </SiteSelectorContext.Provider>
  );
};

const initialState = {
  selectedSiteGroup: null,
};
