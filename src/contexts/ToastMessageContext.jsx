import PropTypes from 'prop-types';
import { createContext, useContext, useReducer } from 'react';
import { TOAST_VARIANTS } from '../components/DSToastMessage.jsx';

const ToastMessageContext = createContext();

export const useToastContext = () => useContext(ToastMessageContext);

export const TOAST_ACTIONS = {
  SHOW_TOAST_INFO: 'SHOW_TOAST_INFO',
  SHOW_TOAST_ERROR: 'SHOW_TOAST_ERROR',
  DISMISS_TOAST: 'DISMISS_TOAST',
};

const toastReducer = (state, action) => {
  switch (action.type) {
    case TOAST_ACTIONS.SHOW_TOAST_INFO: {
      const { title, description } = action.payload;
      return {
        ...state,
        isShowing: true,
        title: title,
        description: description,
        variant: TOAST_VARIANTS.INFO,
      };
    }
    case TOAST_ACTIONS.SHOW_TOAST_ERROR: {
      const { title, description } = action.payload;
      return {
        ...state,
        isShowing: true,
        title: title,
        description: description,
        variant: TOAST_VARIANTS.ERROR,
      };
    }
    case TOAST_ACTIONS.DISMISS_TOAST: {
      return {
        ...toastInitialState,
      };
    }
    default:
      return state;
  }
};

const toastInitialState = {
  isShowing: false,
  title: '',
  description: '',
  variant: TOAST_VARIANTS.INFO,
};

const ToastMessageContextProvider = ({ children }) => {
  const [toastState, toastDispatch] = useReducer(
    toastReducer,
    toastInitialState,
  );

  return (
    <ToastMessageContext.Provider value={{ toastState, toastDispatch }}>
      {children}
    </ToastMessageContext.Provider>
  );
};

ToastMessageContextProvider.propTypes = {
  children: PropTypes.node,
};

export default ToastMessageContextProvider;
