import PropTypes from 'prop-types';
import { createContext, useReducer, useContext } from 'react';
import { DateTime } from 'luxon';

const ScheduleContext = createContext(null);
const ScheduleDispatchContext = createContext(null);

export const useSchedule = () => useContext(ScheduleContext);
export const useScheduleDispatch = () => useContext(ScheduleDispatchContext);

export const SCHEDULE_ACTIONS = {
  INIT: 'INIT',
  UPDATE_POD_DROPDOWN: 'UPDATE_POD_DROPDOWN',
  UPDATE_LOCATION_DROPDOWN: 'UPDATE_LOCATION_DROPDOWN',
  SET_START_DATE: 'SET_START_DATE',
  SET_END_DATE: 'SET_END_DATE',
  SET_START_TIME: 'SET_START_TIME',
  SET_END_TIME: 'SET_END_TIME',
  SET_LOCATION: 'SET_LOCATION',
  SET_IS_CUSTOM_LOCATION: 'SET_IS_CUSTOM_LOCATION',
  SET_ADDRESS_NAME: 'SET_ADDRESS_NAME',
  SET_LOCATION_INFO: 'SET_LOCATION_INFO',
  SET_DRIVER_NOTES: 'SET_DRIVER_NOTES',
  RESET: 'RESET',
};

const scheduleReducer = (state, action) => {
  switch (action.type) {
    case SCHEDULE_ACTIONS.INIT: {
      return {
        ...state,
        ...action.payload,
      };
    }
    case SCHEDULE_ACTIONS.UPDATE_POD_DROPDOWN: {
      const { podOptions, selectedPod } = action.payload;
      return {
        ...state,
        podDropdownOptions: podOptions,
        selectedPod,
        selectedStartDate: null,
        minimumStartDate: null,
        selectedStartTime: null,
        selectedEndDate: null,
        selectedEndTime: null,
        selectedLocation: null,
        isCustomLocation: false,
        locationDropdownOptions: state.locationDropdownOptions,
        customStartTime: '',
        addressName: '',
        locationInfo: '',
        driverNotes: '',
        podOpenTime: '',
        podCloseTime: '',
      };
    }
    case SCHEDULE_ACTIONS.UPDATE_LOCATION_DROPDOWN: {
      const { locationOptions, selectedLocation } = action.payload;
      return {
        ...state,
        locationDropdownOptions: locationOptions,
        selectedLocation,
      };
    }
    case SCHEDULE_ACTIONS.SET_START_DATE: {
      const { selectedStartDate } = action.payload;
      return {
        ...state,
        selectedStartDate,
        selectedStartTime: null,
        selectedEndDate: null,
        selectedEndTime: null,
      };
    }
    case SCHEDULE_ACTIONS.SET_START_TIME: {
      const { selectedStartTime } = action.payload;
      return {
        ...state,
        selectedStartTime,
        selectedEndDate: null,
        selectedEndTime: null,
      };
    }
    case SCHEDULE_ACTIONS.SET_END_DATE: {
      const { selectedEndDate } = action.payload;
      return {
        ...state,
        selectedEndDate,
        selectedEndTime: null,
      };
    }
    case SCHEDULE_ACTIONS.SET_END_TIME: {
      const { selectedEndTime } = action.payload;
      return {
        ...state,
        selectedEndTime,
      };
    }
    case SCHEDULE_ACTIONS.SET_LOCATION: {
      const { selectedLocation } = action.payload;
      if (selectedLocation?.name === 'Custom Location') {
        return {
          ...state,
          selectedLocation,
          addressName: '',
          locationInfo: '',
          isCustomLocation: true,
        };
      }
      return {
        ...state,
        selectedLocation,
        addressName: selectedLocation?.address,
        locationInfo: selectedLocation?.location_info,
        isCustomLocation: false,
      };
    }
    case SCHEDULE_ACTIONS.SET_ADDRESS_NAME: {
      const { addressName } = action.payload;
      return {
        ...state,
        addressName,
      };
    }
    case SCHEDULE_ACTIONS.SET_LOCATION_INFO: {
      const { locationInfo } = action.payload;
      return {
        ...state,
        locationInfo,
      };
    }
    case SCHEDULE_ACTIONS.SET_DRIVER_NOTES: {
      const { driverNotes } = action.payload;
      return {
        ...state,
        driverNotes,
      };
    }
    case SCHEDULE_ACTIONS.RESET: {
      return {
        ...initialState,
      };
    }
    default: {
      return state;
    }
  }
};

export const ScheduleProvider = ({ children }) => {
  const [state, dispatch] = useReducer(scheduleReducer, initialState);
  return (
    <ScheduleContext.Provider value={state}>
      <ScheduleDispatchContext.Provider value={dispatch}>
        {children}
      </ScheduleDispatchContext.Provider>
    </ScheduleContext.Provider>
  );
};

ScheduleProvider.propTypes = {
  //   siteConfig: PropTypes.object,
  children: PropTypes.node,
};

const initialState = {
  selectedPod: null,
  selectedStartDate: null,
  minimumStartDate: null,
  selectedStartTime: null,
  selectedEndDate: null,
  selectedEndTime: null,
  selectedLocation: null,
  isCustomLocation: false,
  podDropdownOptions: [],
  locationDropdownOptions: [],
  customStartTime: '',
  addressName: '',
  locationInfo: '',
  driverNotes: '',
};
