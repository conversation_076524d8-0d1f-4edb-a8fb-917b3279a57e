import { useRef, useState, forwardRef } from 'react';
import tw from 'twin.macro';
import styled from 'styled-components';
import FullCalendar from '@fullcalendar/react';
import resourceTimelinePlugin from '@fullcalendar/resource-timeline';
import resourceTimeGridPlugin from '@fullcalendar/resource-timegrid';
import luxonPlugin from '@fullcalendar/luxon3';
import { DateTime } from 'luxon';

import {
  DSButton,
  DSPodLocalClock,
  DSFilterToggle,
  DSDatePicker,
  DSLocationToggle,
  DSEvent,
  DSEventQuickView,
} from './';
import { ALIGN_OPTIONS } from './DSFilterToggle';
import { ICON_DIRECTION } from './svg/icons/ChevronLeft';
import { ChevronLeft, Calendar, SquareUnion } from './svg/icons';
import { useOutsideClicked } from '../hooks/index.mjs';
import { SessionAdapter } from '../utils/common';
import { SCHEDULES_COLORS } from '../utils/constants';
import { DSAlertPrompt } from '../components';
import API from '../utils/api';
import { TOAST_VARIANTS } from './DSToastMessage';

const FC_LICENCE_KEY = import.meta.env.VITE_FC_LICENSE_KEY;

const DISPLAY_ENUM = {
  HIDE_MOBILES: 'Hide Mobiles',
  HIDE_CARTS: 'Hide Carts',
};

const CustomDateButton = forwardRef(({ onClick, isOpen }, ref) => {
  return (
    <DSButton
      className="mb-2"
      ref={ref}
      icon={<Calendar />}
      variant="outline"
      onClick={onClick}
      selected={isOpen}
      hug
    />
  );
});

const StyledFullCalendarHeader = styled.div`
  ${tw`
    flex flex-row
    items-center
    justify-between
    px-4 py-[30px]
    sticky top-[134px]
    z-10
    bg-dslWhite
  `};

  .sfc-header-section {
    ${tw`
      flex flex-row
      items-center
      gap-4
      font-sans
    `};

    .sfc-header-title {
      ${tw`
        text-2xl font-bold uppercase text-dslBlack-2 
      `};
    }
  }
`;

const StyledFullCalendarContainer = styled.div`
  ${tw`font-sans! font-bold text-dslGray-4 pb-48 z-1`};

  * {
    ${tw`border-dslGray-1!`};
  }

  .fc-header-toolbar {
    ${tw`mb-0!`};
  }

  .fc-non-business {
    ${tw`bg-dslGray-3!`};
  }

  .fc-datagrid-cell[role='gridcell'] {
    .fc-datagrid-cell-cushion {
      ${tw`text-dslBlack-2 font-bold`};
    }
  }

  .fc-timeline-slot-frame {
    ${tw`h-14!`};
  }

  .fc-scrollgrid thead[role='rowgroup'] .fc-datagrid-cell-frame:first-child {
    ${tw`h-[113px]!`};
  }

  .fc-timeline-header-row-chrono .fc-timeline-slot-frame {
    ${tw`justify-center`};
  }

  .fc-datagrid-cell-frame {
    ${tw`flex flex-col items-center justify-center`};
    .fc-datagrid-cell-cushion {
      ${tw`p-0 w-full text-center`};
    }
  }

  .fc-datagrid-header {
    ${tw`uppercase`};
    col:first-child {
      ${tw`w-full`};
    }
    col:last-child {
      ${tw`hidden`};
    }
    .fc-datagrid-cell:first-child .fc-datagrid-cell-frame {
      ${tw`w-full justify-center`};
    }
  }

  .fc-resource-timeline-divider {
    ${tw`bg-dslGray-1`};
  }

  .sfc-resource-lane {
    .fc-timeline-lane-frame {
      ${tw`min-h-[70px]`};
    }
  }

  .fc-scrollgrid-sync-table .fc-resource .fc-datagrid-cell-frame {
    ${tw`min-h-[70px] uppercase`};
  }

  .sfc-first-gear-lane {
    ${tw`border-b-transparent!`};
  }

  .fc-timeline-event {
    ${tw`border-0!`}
  }

  .fc-scrollgrid-section-body {
    .fc-day-today {
      ${tw`bg-dslBlue-light`}
    }
  }

  .fc-event {
    ${tw`
      bg-dslWhite
      font-medium 
      h-10 
      mt-4 
      rounded
    `}
  }

  .fc-daygrid-body {
    ${tw`hidden`}
  }

  .fc-col-header-cell.fc-resource {
    ${({ vertical }) => (vertical ? tw`hidden` : tw``)}
  }

  .fc-timegrid-slot {
    ${tw`h-16`}
  }

  .fc-day-today {
    ${({ vertical }) => (vertical ? tw`bg-dslBlue-light` : tw``)}

    a {
      ${({ vertical }) => (vertical ? tw`text-dslBlue-dark` : tw``)}
    }
  }

  .fc-direction-ltr .fc-timegrid-slot-label-frame {
    ${tw`text-center`}
  }

  .fc-timegrid-divider {
    ${({ vertical }) => (vertical ? tw`p-0` : tw``)}
  }

  .fc-col-header-cell {
    ${tw`py-4`}
  }

  .fc-timegrid-event {
    ${tw`p-0`}
  }

  .fc-timegrid-col-events {
    .fc-timegrid-event-harness {
      ${tw`border-none!`}
    }
  }

  .fc-timegrid-event-harness {
    ${tw`border-0`}
  }

  .fc-scrollgrid-section-sticky {
    ${tw`sticky top-[248px] z-10 uppercase`}
  }

  table tbody .fc-scrollgrid-sync-table tr {
    height: 70px;
  }
`;

const StyledRenderDeleteAlertBody = styled.div`
  div {
    ${tw`
  font-normal
  `}
  }
  span {
    ${tw`
        uppercase
        font-bold  
      `}
  }

  .italics-text {
    ${tw`
        italic
      `}
  }
`;
const DSSchedule = ({
  siteInfo,
  globalDate,
  setGlobalDate,
  siteLocations = [],
  mobileResources = [],
  mobileSiteSchedules = {},
  onDelete,
}) => {
  const calendarRef = useRef(null);
  const filterRef = useRef(null);
  const locationRef = useRef(null);

  const [viewTitle, setViewTitle] = useState('');
  const [filterOpen, setFilterOpen] = useState(false);
  const [locationOpen, setLocationOpen] = useState(false);
  const [showEventDetails, setShowEventDetails] = useState(false);
  const [selectedLocations, setSelectedLocations] = useState([]);
  const [selectedEvent, setSelectedEvent] = useState(null);
  const [eventPosX, setEventPosX] = useState(0);
  const [eventPosY, setEventPosY] = useState(0);
  const [selectedFilters, setSelectedFilters] = useState([]);
  const [showDeleteAlert, setShowDeleteAlert] = useState(false);
  const [deletingSession, setDeletingSession] = useState(false);

  const slotMinWidth = '88';
  const displayOptions = [DISPLAY_ENUM.HIDE_MOBILES, DISPLAY_ENUM.HIDE_CARTS];
  const timezone = siteInfo?.tz;

  useOutsideClicked(filterRef, () => {
    setFilterOpen(false);
  });

  useOutsideClicked(locationRef, () => {
    setLocationOpen(false);
  });

  const getCalendarApi = () => {
    return calendarRef.current?.getApi();
  };

  const onToggleFilter = (e) => {
    const filter = e.target.value;
    if (selectedFilters.includes(filter)) {
      setSelectedFilters(selectedFilters.filter((item) => item !== filter));
    } else {
      setSelectedFilters([...selectedFilters, filter]);
    }
  };

  const onClearAllFilters = () => {
    setSelectedFilters([]);
  };

  const onLocationFilter = (e) => {
    const location = e.target.value;
    if (selectedLocations.includes(location)) {
      setSelectedLocations(
        selectedLocations.filter((item) => item !== location),
      );
    } else {
      setSelectedLocations([...selectedLocations, location]);
    }
  };

  const onClearAllLocations = () => {
    setSelectedLocations([]);
  };

  const onChangeDate = (date) => {
    const calendarApi = getCalendarApi();
    calendarApi?.gotoDate(date);
    setGlobalDate(date.toISOString());
  };

  const uniquePods = Array.from(
    new Set(
      mobileResources.map((resource) => resource?.id || resource?.site_id),
    ),
  ).filter(Boolean);

  const getStartOfWeek = () => {
    const calendarApi = getCalendarApi();
    const selectedDate = calendarApi?.getDate();
    return DateTime.fromJSDate(selectedDate || new Date())
      .startOf('week')
      .toJSDate();
  };

  const getEndOfWeek = () => {
    const calendarApi = getCalendarApi();
    const selectedDate = calendarApi?.getDate();
    return DateTime.fromJSDate(selectedDate || new Date())
      .endOf('week')
      .toJSDate();
  };

  const handleOnViewChange = (arg) => {
    const { view } = arg;
    const calendarApi = getCalendarApi();
    const title = calendarApi
      ? DateTime.fromJSDate(calendarApi.getDate())
          .setZone(timezone)
          .toFormat('MMMM, yyyy')
      : '';
    setViewTitle(title);
  };

  const handleOnTodayPressed = () => {
    const calendarApi = getCalendarApi();
    calendarApi?.today();
    setGlobalDate(calendarApi?.getDate()?.toISOString());
  };

  const handleOnPrevPressed = () => {
    const calendarApi = getCalendarApi();
    calendarApi?.prev();
    setGlobalDate(calendarApi?.getDate()?.toISOString());
  };

  const handleOnNextPressed = () => {
    const calendarApi = getCalendarApi();
    calendarApi?.next();
    setGlobalDate(calendarApi?.getDate()?.toISOString());
  };

  const onClickEvent = async ({ el, event }) => {
    const sessionData = SessionAdapter.fromFullCalendarEvent(event);
    const rect = el.getBoundingClientRect();

    const scrollX = window.scrollX;
    const scrollY = window.scrollY;

    let x = rect.left + scrollX;
    let y = rect.top + scrollY;

    const popoverWidth = 450;
    const popoverHeight = 450;

    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;

    const margin = 10;
    if (x + popoverWidth + margin > viewportWidth + scrollX) {
      x = viewportWidth + scrollX - popoverWidth - margin;
    }

    if (x < scrollX + margin) {
      x = scrollX + margin;
    }

    if (y + popoverHeight + margin > viewportHeight + scrollY) {
      y = viewportHeight + scrollY - popoverHeight - margin;
    }

    if (y < scrollY + margin) {
      y = scrollY + margin;
    }

    setEventPosX(x);
    setEventPosY(y);
    setSelectedEvent(sessionData);
    setShowEventDetails(true);
  };

  const handleOnCloseEventDetails = () => {
    setShowEventDetails(false);
  };

  const handleOnDiscard = () => {
    handleOnCloseEventDetails();
  };

  const handleOnDelete = () => {
    setShowDeleteAlert(true);
  };

  const handleOnEditDraft = () => {
    // handleOnCloseEventDetails();
    // onEditDraft(selectedEvent);
  };

  const confirmClearReservations = async () => {
    // await fetchSessionReservations(selectedEvent?.session_id);
    // setShowClearReservationsAlert(true);
  };

  // Create dynamic title map from siteLocations
  const titleMap = siteLocations.reduce((map, location) => {
    if (location.location_info && location.alias_groups?.[0]) {
      map[location.location_info] = location.alias_groups[0];
    }
    return map;
  }, {});

  // Transform resources to FullCalendar format
  const transformedResources = (mobileResources || [])
    .filter((resource) => {
      const siteId = resource?.site_id || 'unknown';
      return selectedFilters.length === 0 || selectedFilters.includes(siteId);
    })
    .map((resource) => ({
      id: resource?.site_id || 'unknown',
      title: resource?.site_id || 'Unknown Resource',
    }));

  // Track unique location_info values and assign colors
  const locationInfoMap = new Map();
  let nonCustomColorIndex = 0;
  let customColorIndex = 0;

  // Transform events to FullCalendar format
  const transformedEvents = Object.keys(mobileSiteSchedules || {})
    .filter((siteId) => {
      // Apply mobile resource filter
      return selectedFilters.length === 0 || selectedFilters.includes(siteId);
    })
    .flatMap((siteId) =>
      (mobileSiteSchedules[siteId] || [])
        .filter((event) => {
          // Apply location filter based on site_group_location_id
          const locationId = event?.site_group_location_id;
          return (
            selectedLocations.length === 0 ||
            (locationId !== null && selectedLocations.includes(locationId))
          );
        })
        .map((event) => {
          const isCustom = event?.site_group_location_id === null;
          const locationInfo = event?.location_info || 'Unknown Location';

          // Get or assign color index for this location_info
          let colorIndex;
          if (locationInfoMap.has(locationInfo)) {
            colorIndex = locationInfoMap.get(locationInfo);
          } else {
            if (isCustom) {
              colorIndex = 6 + (customColorIndex % 2); // Use colors 6 and 7
              customColorIndex++;
            } else {
              colorIndex = nonCustomColorIndex % 6; // Use colors 0-5
              nonCustomColorIndex++;
            }
            locationInfoMap.set(locationInfo, colorIndex);
          }

          const colors = SCHEDULES_COLORS[colorIndex] || SCHEDULES_COLORS[0];

          return {
            id:
              event?.location_schedule_id || `event-${siteId}-${Math.random()}`,
            resourceId: siteId || 'unknown',
            title: titleMap[locationInfo] || locationInfo,
            start: event?.start_timestamp || new Date(),
            end: event?.end_timestamp || new Date(),
            backgroundColor: colors.background,
            borderColor: colors.border,
            extendedProps: {
              address: event?.address || '',
              notes: event?.notes || '',
              site_group_id: event?.site_group_id || null,
              site_group_location_id: event?.site_group_location_id || null,
              available_start_timestamp:
                event?.available_start_timestamp || null,
              available_end_timestamp: event?.available_end_timestamp || null,
              location_info: event?.location_info || null,
            },
          };
        }),
    );

  const renderDeleteAlertBody = () => {
    const { title_name, resourceId, start_timestamp, end_timestamp } =
      selectedEvent;
    const startDateTime = DateTime.fromISO(start_timestamp, {
      zone: timezone,
    });
    const startDate = startDateTime.toFormat('MMMM d, h:mma');
    const endDateTime = DateTime.fromISO(end_timestamp, { zone: timezone });
    const endDate = endDateTime.toFormat('MMMM d, h:mma z');
    return (
      <StyledRenderDeleteAlertBody>
        <div>
          {' '}
          This action will delete <span>{resourceId}’s</span> schedule for{' '}
          <span>
            {startDate} - {endDate}
          </span>{' '}
          at <span>{title_name}</span>
        </div>
        <br />
        <div>Are you sure you want to delete this schedule? </div>
        <br />
        <div className="italics-text">
          Note: This operation cannot be undone.
        </div>
      </StyledRenderDeleteAlertBody>
    );
  };

  const deleteReservations = async (mobileSiteId, locationScheduleId) => {
    try {
      const res = await API.removeReservationFromSession(
        siteInfo?.site_id,
        mobileSiteId,
        locationScheduleId,
      );
      onDelete({
        variant: TOAST_VARIANTS.INFO,
        title: 'Delete successful',
        description: `The schedule for ${mobileSiteId} has been successfully deleted from the calendar.`,
      });
    } catch (e) {
      if (e?.status === 409) {
        onDelete({
          variant: TOAST_VARIANTS.ERROR,
          title: 'Notice - Cannot Delete Schedule',
          description: e?.message,
        });
      } else {
        onDelete({
          variant: TOAST_VARIANTS.ERROR,
          title: 'Notice - Cannot Delete Schedule',
          description: e?.message,
        });
      }
    } finally {
      setDeletingSession(false);
    }
  };

  const handleClearReservations = async () => {
    setDeletingSession(true);
    setShowDeleteAlert(false);
    deleteReservations(selectedEvent.resourceId, selectedEvent.session_id);
  };

  return (
    <>
      <StyledFullCalendarHeader>
        <div className="sfc-header-section">
          <DSButton
            label="TODAY"
            hug
            variant="outline"
            onClick={handleOnTodayPressed}
          />
          <DSButton
            icon={<ChevronLeft />}
            variant="outline"
            hug
            onClick={handleOnPrevPressed}
          />
          <DSButton
            icon={<ChevronLeft rotate={ICON_DIRECTION.RIGHT} />}
            variant="outline"
            hug
            onClick={handleOnNextPressed}
          />
          <div className="sfc-header-title">{viewTitle}</div>
        </div>
        <div className="sfc-header-section">
          <DSPodLocalClock timezone={timezone} />
        </div>
        <div className="sfc-header-section">
          <DSFilterToggle
            ref={filterRef}
            open={filterOpen}
            options={{
              Display_Options: { options: displayOptions },
              Mobile_Resources: { options: uniquePods, disabled: false },
            }}
            onClick={() => setFilterOpen(!filterOpen)}
            selectedFilters={selectedFilters}
            onToggleFilter={onToggleFilter}
            onClearAllFilters={onClearAllFilters}
            align={ALIGN_OPTIONS.RIGHT}
            clearButtonText="Reset"
          />
          <DSDatePicker
            type="month"
            customInput={CustomDateButton}
            selectedDate={getCalendarApi()?.getDate()}
            onChange={onChangeDate}
            startDate={getStartOfWeek()}
            endDate={getEndOfWeek()}
            popperPlacement="bottom"
          />
          <div tw="min-w-44">
            <DSLocationToggle
              ref={locationRef}
              open={locationOpen}
              options={siteLocations}
              onClick={() => setLocationOpen(!locationOpen)}
              selectedLocations={selectedLocations}
              onToggleLocation={onLocationFilter}
              onClearAllLocations={onClearAllLocations}
              align={ALIGN_OPTIONS.RIGHT}
              clearButtonText="Reset"
            />
          </div>
        </div>
      </StyledFullCalendarHeader>
      <StyledFullCalendarContainer>
        <FullCalendar
          plugins={[
            luxonPlugin,
            resourceTimelinePlugin,
            resourceTimeGridPlugin,
          ]}
          timeZone={timezone}
          schedulerLicenseKey={FC_LICENCE_KEY}
          ref={calendarRef}
          initialView="resourceTimelineMonth"
          viewClassNames={handleOnViewChange}
          titleRangeSeparator="-"
          headerToolbar={{
            start: false,
            center: false,
            end: false,
          }}
          resources={transformedResources}
          resourceAreaWidth={'200px'}
          resourceAreaColumns={[
            {
              group: true,
              field: 'title',
              headerContent: 'Mobile resources',
            },
            {
              field: 'title',
              width: '200px',
            },
          ]}
          resourceGroupLabelClassNames={'sfc-group-label'}
          nowIndicator={true}
          height={'auto'}
          slotMinWidth={slotMinWidth}
          slotLabelInterval={'24:00'}
          slotLabelFormat={[
            {
              month: 'long',
              year: 'numeric',
            },
            {
              weekday: 'short',
              day: 'numeric',
            },
          ]}
          dayHeaderContent={(arg) => {
            const { date } = arg;
            return DateTime.fromJSDate(date)
              .setZone(timezone)
              .toFormat('ccc d');
          }}
          eventTimeFormat={{
            hour: 'numeric',
            minute: '2-digit',
            omitZeroMinute: true,
            meridiem: 'short',
          }}
          eventContent={(arg) => <DSEvent arg={arg} timezone={timezone} />}
          displayEventTime
          displayEventEnd
          eventClick={onClickEvent}
          firstDay={1}
          allDayContent=""
          stickyHeaderDates
          initialDate={globalDate}
          resourceAreaHeaderClassNames="fc-scrollgrid-section-sticky"
          resourceLabelContent={(arg) => {
            return (
              <div
                style={{
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center',
                }}
              >
                <SquareUnion />
                <span>{arg.resource.id}</span>
              </div>
            );
          }}
          events={transformedEvents}
        />
        {showEventDetails && (
          <DSEventQuickView
            onClose={handleOnCloseEventDetails}
            onDiscard={handleOnDiscard}
            onDelete={handleOnDelete}
            onEdit={handleOnEditDraft}
            event={selectedEvent}
            xPos={eventPosX}
            yPos={eventPosY}
            timezone={timezone}
            onClearReservations={confirmClearReservations}
          />
        )}
        {showDeleteAlert && (
          <DSAlertPrompt
            title="Confirm Schedule Cancellation"
            body={renderDeleteAlertBody()}
            onCancel={() => setShowDeleteAlert(false)}
            onConfirm={() => handleClearReservations()}
            loading={deletingSession}
            actionText="Delete"
            cancelText="Cancel"
            danger
          />
        )}
      </StyledFullCalendarContainer>
    </>
  );
};

export default DSSchedule;
