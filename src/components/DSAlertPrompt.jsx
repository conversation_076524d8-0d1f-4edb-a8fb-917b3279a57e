import PropTypes from 'prop-types';
import styled from 'styled-components';
import tw from 'twin.macro';
import { createPortal } from 'react-dom';

import DSButton from './DSButton';
import DSLoadingSpinner from './DSLoadingSpinner';
import { PromptIcon, PromptErrorIcon } from './svg';

const StyledAlertOverlay = styled.div`
  ${tw`
    flex items-center justify-center
    absolute top-0 left-0
    z-[99999999999]
    w-full h-full
    bg-dslBlack-faded-50
  `};
`;

const StyledAlertContainer = styled.div`
  ${tw`
    flex
    justify-center items-center
    bg-dslBlack-2
    text-dslWhite
    min-h-[368px]
    py-12
    overflow-hidden
  `};
  ${({ fullWidth }) => (!fullWidth ? tw`max-w-[55.25rem]` : tw`w-full`)}

  .ds-alert-prompt__wrapper {
    ${tw`
      flex flex-col flex-1
      text-center
      items-center
      justify-center
      w-[1024px]
    `};
  }

  .ds-alert-prompt__title-container {
    ${tw`
      flex flex-col flex-1
      items-center
    `};
  }

  .ds-alert-prompt__title {
    ${tw`
      text-xl
      md:text-[26px]
      leading-[24px]
      font-bold
      pt-2.5
    `};
  }

  .ds-alert-prompt__body {
    ${tw`
      text-base pt-2.5
      whitespace-pre-line
      max-h-96
      w-full
    `};
    ${({ fullWidth }) => (!fullWidth ? tw`max-w-[25rem]` : tw`w-full`)}
  }

  .ds-alert-prompt__button-container {
    ${tw`
      flex flex-row gap-6
      justify-center
      w-[320px] md:w-[356px]
      mt-7
    `};
  }
`;

const DSAlertPrompt = ({
  title,
  body,
  onCancel,
  onConfirm,
  loading,
  cancelText,
  actionText,
  danger,
  fullWidth = true,
}) => {
  return createPortal(
    <StyledAlertOverlay>
      <StyledAlertContainer fullWidth={fullWidth}>
        <div className="ds-alert-prompt__wrapper">
          <div className="ds-alert-prompt__title-container">
            {danger ? <PromptErrorIcon /> : <PromptIcon />}
            <div className="ds-alert-prompt__title">{title}</div>
            <div className="ds-alert-prompt__body">{body}</div>
          </div>
          <div className="ds-alert-prompt__button-container">
            <DSButton
              label={cancelText ? cancelText : 'Go Back'}
              variant="secondary"
              onClick={onCancel}
              disabled={loading}
              dark
              hug
            />
            <DSButton
              label={loading ? '' : actionText ? actionText : 'Ok'}
              variant={danger ? 'danger' : 'primary'}
              onClick={onConfirm}
              icon={loading ? <DSLoadingSpinner /> : undefined}
              disabled={loading}
              hug
            />
          </div>
        </div>
      </StyledAlertContainer>
    </StyledAlertOverlay>,
    document.body,
  );
};

DSAlertPrompt.propTypes = {
  title: PropTypes.string.isRequired,
  body: PropTypes.oneOfType([PropTypes.string, PropTypes.element]),
  onCancel: PropTypes.func.isRequired,
  onConfirm: PropTypes.func.isRequired,
  loading: PropTypes.bool,
  cancelText: PropTypes.string,
  actionText: PropTypes.string,
  danger: PropTypes.bool,
  fullWidth: PropTypes.bool,
};

export default DSAlertPrompt;
