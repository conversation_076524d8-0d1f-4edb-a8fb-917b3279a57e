import { useState } from 'react';
import PropTypes from 'prop-types';
import styled from 'styled-components';
import tw from 'twin.macro';
import { NavLink } from 'react-router-dom';

import { DSLoadingSpinner } from '../../components';
import { ChevronLeft } from '../svg/icons';
import DSNavItem, { NAV_LABELS } from './DSNavItem';
import bgSideNav from '../../assets/side_nav_bg_image.jpg';
import { reconcileNavItems } from '../../utils/common';
import { LogoHorizontalDark, LogoControlCenterDark } from '../svg';
import logoSmall from '../../assets/CC_Logo_Mini.svg';
import packageJson from '../../../package.json';
import useBreakPoint from '../../hooks/useBreakPoint';
import API from '../../utils/local_api';

const StyledNavContainer = styled.div`
  ${tw`
    relative
    flex flex-col
    h-full
    box-border
    border-r border-dslGray-2
    bg-no-repeat bg-bottom bg-white
    transition-all duration-300 ease-in-out
    z-10
  `};

  ${({ expanded }) => (expanded ? tw`w-64` : tw`w-16`)};

  .ds-sidenav-item-list {
    ${tw`
      flex flex-col
      gap-2.5
    `};
    ${({ expanded }) =>
      expanded ? tw`overflow-x-hidden` : tw`overflow-x-visible`}
  }
`;

const StyledHelpButton = styled.button`
  ${tw`
  md:bg-dslBlack-1
  md:bg-opacity-5
  md:backdrop-blur-md
  cursor-pointer
`};
  ${({ loading }) => loading && tw`flex justify-center p-4`}
`;
const StyledCollapseButton = styled.button`
  ${tw`
    absolute
    z-50
    top-12 right-[-15px]
    p-0.5
    bg-dslWhite
    border border-dslGray-2
    rounded
    opacity-0
    transition-all duration-200 ease-in-out
    hover:(bg-dslBlue-dark border-transparent)
    xs:hidden
    sm:hidden
    md:block
  `};

  ${StyledNavContainer}:hover & {
    ${tw`opacity-100`};
  }

  &:hover > svg > path {
    ${tw`transition-all duration-200 ease-in-out`};
    ${tw`fill-dslWhite`};
  }

  svg {
    ${tw`transition-all duration-200 ease-in-out`};
    ${({ expanded }) => (expanded ? tw`rotate-0` : tw`rotate-180`)};
  }
`;

const StyledMobileFooter = styled.div`
  ${tw`
    flex flex-col
    md:hidden
  `}

  .logo-container {
    ${tw`
    flex flex-col
    items-center
    justify-center
    py-5
    gap-5
  `}
  }
`;

const StyledVersionDetails = styled.div`
  ${tw`
    flex
    flex-col
    items-center
    px-5
    py-3
    w-full
    border-t-2
  `}

  .privacy-text {
    ${tw`
      text-[11px]
      text-dslGray-3
      cursor-pointer
      underline
    `}
  }

  .version-details-text {
    ${tw`
      text-[11px]
      text-dslGray-3
      ml-1
    `}
  }
`;

const StyledMobileNavigation = styled.div`
  ${tw`
    absolute
    w-full
    top-[86px] // height of top header bar
    left-0
    right-0
    bottom-0
    transition-all duration-300 ease-in-out
    z-40
  `}

  .mobile-nav-overlay {
    ${tw`
      absolute
      bg-dslBlack-faded-70
      w-full
      h-full
    `}
  }

  .mobile-nav-cont {
    ${tw`
      absolute
      w-3/5
      h-full
      bg-dslWhite
      flex
      flex-col
      justify-between
    `}

    .mobile-nav-items {
      ${tw`
        flex flex-col
        gap-2.5  
      `}
    }
  }
`;

const DSNavContainer = ({
  expanded = true,
  navPrefix = '/app',
  userRole,
  isSideMenuOpen,
}) => {
  const breakpoint = useBreakPoint();
  const isMobile = breakpoint === 'xs' || breakpoint === 'sm';
  const [isExpanded, setIsExpanded] = useState(isMobile ? false : expanded);

  const handleChangeExpanded = () => {
    const newExpanded = !isExpanded;
    setIsExpanded(newExpanded);
  };

  const renderNavItems = () => {
    const activeNavItems = [
      {
        label: 'DASHBOARD',
        path: 'dashboard',
        isAdmin: true,
      },
      {
        label: 'PEOPLE MANAGEMENT',
        path: 'people-management',
        isAdmin: true,
      },
    ];
    return activeNavItems
      .filter((item) => !item.hidden)
      .map((item, index) => {
        const toPath = item.path ? `${navPrefix}/${item.path}` : navPrefix;
        return (
          <NavLink end={toPath === navPrefix} to={toPath} key={index}>
            {({ isActive }) => (
              <DSNavItem
                active={isActive}
                isExpanded={isMobile ? true : isExpanded}
                label={item.label}
              />
            )}
          </NavLink>
        );
      });
  };

  const renderMobileNavBar = () => {
    if (!isSideMenuOpen) {
      return null;
    }
    return (
      <StyledMobileNavigation>
        <div className="mobile-nav-overlay" />
        <div className="mobile-nav-cont">
          <div className="mobile-nav-items">{renderNavItems()}</div>
          <StyledMobileFooter>
            <div className="logo-container">
              <LogoHorizontalDark />
              <LogoControlCenterDark />
            </div>
            <StyledVersionDetails>
              <div className="version-details-text">
                <a
                  href="https://www.dreamscapelearn.com/privacy"
                  className="privacy-text"
                  target="_blank"
                  rel="noreferrer noopener"
                >
                  Privacy Policy
                </a>
              </div>
              <div tw="flex flex-row py-5">
                <img src={logoSmall} alt="logo-small" />
                <div className="version-details-text">
                  DS Control Center Version {packageJson.version}
                </div>
              </div>
            </StyledVersionDetails>
          </StyledMobileFooter>
        </div>
      </StyledMobileNavigation>
    );
  };

  if (isMobile) {
    return renderMobileNavBar();
  }

  return (
    <StyledNavContainer
      expanded={isExpanded}
      tw="flex flex-col justify-between"
    >
      <StyledCollapseButton
        expanded={isExpanded}
        onClick={handleChangeExpanded}
      >
        <ChevronLeft />
      </StyledCollapseButton>

      <div tw="h-full md:flex flex-col justify-between">
        <div className="ds-sidenav-item-list">{renderNavItems()}</div>
      </div>
    </StyledNavContainer>
  );
};

DSNavContainer.propTypes = {
  expanded: PropTypes.bool,
  userRole: PropTypes.string,
  navPrefix: PropTypes.string,
  hasPods: PropTypes.bool,
  isSideMenuOpen: PropTypes.bool,
};

export default DSNavContainer;
