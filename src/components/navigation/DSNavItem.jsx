/**
 * This component ignore the use of the SVG component's "color" prop
 * for a couple of reasons:
 * 1. Handling the hover state of the icon is easier with CSS
 * 2. CSS is the only way to match the transition timing of the rest of the nav item.
 */
import { useState, useEffect, useRef } from 'react';
import PropTypes from 'prop-types';
import styled from 'styled-components';
import tw from 'twin.macro';
import { Account, Home, PeopleManagement, SignOut } from '../svg/icons';
import DSTooltip from '../DSTooltip';

export const NAV_LABELS = {
  DASHBOARD: 'DASHBOARD',
  PEOPLE_MANAGEMENT: 'PEOPLE MANAGEMENT',
  ACCOUNT_DETAILS: 'ACCOUNT DETAILS',
  SIGN_OUT: 'SIGN OUT',
};

const ICON_MAP = {
  [NAV_LABELS.DASHBOARD]: Home,
  [NAV_LABELS.PEOPLE_MANAGEMENT]: PeopleManagement,
  [NAV_LABELS.ACCOUNT_DETAILS]: Account,
  [NAV_LABELS.SIGN_OUT]: SignOut,
};

const StyledNavItem = styled.div`
  ${tw`
    relative
    flex
    flex-row
    items-center
    text-dslGray-4
    xs:max-sm:text-xs
    text-sm
    uppercase
    py-[22px]
    transition-all duration-300 ease-in-out
  `};

  ${({ active }) =>
    active
      ? tw`bg-dslBlack-2 text-dslWhite`
      : tw`hover:(bg-dslBlue-light text-dslBlue-dark)`};

  ${({ disabled }) =>
    disabled ? tw`pointer-events-none cursor-default opacity-30` : tw``}
`;

const StyledIconHolder = styled.div`
  ${tw`
    flex items-center justify-center
    h-5
    w-16
  `};

  svg {
    path {
      ${tw`transition-all duration-300 ease-in-out`};
    }
  }
`;

const StyledInactiveNavItemIconHolder = styled(StyledIconHolder)`
  svg {
    path {
      ${tw`fill-dslGray-4`};

      ${StyledNavItem}:hover & {
        ${tw`fill-dslBlue-dark`};
      }
    }
  }
`;

const StyledActiveNavItemIconHolder = styled(StyledIconHolder)`
  svg {
    path {
      ${tw`fill-dslWhite`};
    }
  }
`;

const renderIconHolder = (label, active) => {
  const NavIcon = ICON_MAP[label];
  if (active) {
    return (
      <StyledActiveNavItemIconHolder>
        <NavIcon />
      </StyledActiveNavItemIconHolder>
    );
  }

  return (
    <StyledInactiveNavItemIconHolder>
      <NavIcon />
    </StyledInactiveNavItemIconHolder>
  );
};

const DSNavItem = ({ isExpanded, label, active, onClick, disabled }) => {
  const [showTooltip, setShowTooltip] = useState(false);
  const buttonRef = useRef(null);

  useEffect(() => {
    const button = buttonRef?.current;
    if (button) {
      button.addEventListener('mouseenter', () => setShowTooltip(true));
      button.addEventListener('mouseleave', () => setShowTooltip(false));
    }

    return () => {
      if (button) {
        button.removeEventListener('mouseenter', () => setShowTooltip(true));
        button.removeEventListener('mouseleave', () => setShowTooltip(false));
      }
    };
  }, []);
  return (
    <div tw="relative">
      <DSTooltip
        label={label}
        positionStyles={tw`top-5 left-12`}
        show={showTooltip && !isExpanded}
      />
      <StyledNavItem
        label={label}
        active={active}
        expanded={isExpanded}
        onClick={onClick}
        disabled={disabled}
        ref={buttonRef}
      >
        {renderIconHolder(label, active)}
        {isExpanded ? (
          <div tw="whitespace-nowrap absolute left-14">{label}</div>
        ) : null}
      </StyledNavItem>
    </div>
  );
};

DSNavItem.propTypes = {
  isExpanded: PropTypes.bool,
  active: PropTypes.bool,
  label: PropTypes.string,
  onClick: PropTypes.func,
  disabled: PropTypes.bool,
};

export default DSNavItem;
