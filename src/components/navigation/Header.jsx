import { useState, useEffect, useRef } from 'react';
import styled from 'styled-components';
import tw, { theme } from 'twin.macro';
import PropTypes from 'prop-types';
import { useSubmit, useLoaderData } from 'react-router-dom';
import { DateTime } from 'luxon';

import DSSelectDropdown from '../DSSelectDropdown';
import DSBadge from '../DSBadge';
import DSNavItem, { NAV_LABELS } from './DSNavItem';
import { LogoHorizontal } from '../svg';
import ChevronLeft, { ICON_DIRECTION } from '../svg/icons/ChevronLeft';
import logoSmall from '../../assets/CC_Logo_Mini.svg';
import { USER_ROLES, USER_ROLE_DISPLAY } from '../../utils/constants';
import packageJson from '../../../package.json';
import DSUserDetails from '../DSUserDetails';
import { Close, MenuIcon } from '../svg/icons';
import LogoMobile from '../svg/LogoMobile';
import { MrmLogoIcon } from '../../components/svg/icons';
import { SiteGroupEnvironment } from '../../components/svg/icons/small';
import {
  useSiteSelector,
  useSiteSelectorDispatch,
  SITE_SELECTOR_ACTIONS,
} from '../../contexts/SiteSelectorContext';
import Development from '../svg/icons/small/Development';

const StyledHeader = styled.header`
  ${tw`
    flex
    justify-between items-center
    fixed
    z-50
    w-full
    h-full
    px-11
    xs:px-5
    min-h-[86px]
    max-h-[86px]
    bg-dslBlack-2
    text-sm text-dslWhite
    border-b border-dslGray-2
  `};
`;

const StyledLeftContainer = styled.div`
  ${tw`
    md:flex
    hidden
  `};
`;

const StyledLeftMobileContainer = styled.div`
  ${tw`
    md:hidden
    flex flex-row
    items-center
    gap-6
  `};
`;

const StyledCenterContainer = styled.div`
  ${tw`
    md:flex flex-col
    justify-center
    // Roughly centering the logo and the site group dropdown
    ml-32
  `};

  .env-container {
    ${tw`
       flex flex-col items-center gap-2
    `}
  }

  .env-sub-container {
    ${tw`
      text-dslBlack-2
      flex flex-row
      items-center
      mt-2   
    `}
  }
`;
const StyledHamburgerMenu = styled.div`
  ${tw`
    flex flex-row
    items-center
    gap-6
  `};

  svg {
    path {
      ${tw`fill-dslWhite`};
    }
  }
`;

const StyledRightContainer = styled.div`
  ${tw`
    relative
    flex
    gap-2
  `};
`;

const StyledProfileButton = styled.button`
  ${tw`
    flex flex-row
    items-center
    gap-2.5
  `};
`;

const StyledNameGroupContainer = styled.div`
  ${tw`flex flex-row gap-2 items-center text-sm`};
  span {
    ${tw`xs:max-sm:text-xs`}
  }
`;

const StyledMenuContainer = styled.ul`
  ${tw`
    absolute 
    md:top-8 right-0 
    w-56
    bg-dslWhite
    text-dslWhite
    shadow-dslMedium
    transition-all duration-200 ease-in-out
    top-10
    border rounded-[3px]
  `};

  ${({ isOpen }) =>
    isOpen ? tw`opacity-100` : tw`opacity-0 pointer-events-none`};
`;

const StyledMenuButton = styled.button`
  ${tw`w-full`};
`;

const StyledVersionDetails = styled.div`
  ${tw`
    flex
    items-center
    justify-center
    py-2.5
    px-5
    w-full
  `}

  .version-details-text {
    ${tw`
      text-[11px]
      text-dslGray-3
      ml-1
    `}
  }
`;

const StyledTopBorderView = styled.div`
  ${tw`
    flex
    justify-center
    border-t border-dslGray-2
    items-center
    w-full
    p-2
  `}
  .privacy-text {
    ${tw`
    text-[11px]
    text-dslGray-3
    cursor-pointer
    underline
  `}
  }
`;

const USER_BADGE = {
  [USER_ROLES.SUPER_ADMIN]: <DSBadge color="gradient">Super Admin</DSBadge>,
  [USER_ROLES.ADMIN]: <DSBadge>Admin</DSBadge>,
  [USER_ROLES.DISTRICT_MANAGER]: <DSBadge>District Manager</DSBadge>,
  [USER_ROLES.TRUCK_DRIVER]: <DSBadge color="outline">Truck Driver</DSBadge>,
};

const userRoleDropdownOptions = Object.values(USER_ROLES).map((role) => {
  return {
    id: role,
    label: USER_ROLE_DISPLAY[role],
  };
});

const Header = ({ userData = {}, isSideMenuOpen, setIsSideMenuOpen }) => {
  const submit = useSubmit();
  const { allSites } = useLoaderData();
  const { name = '' } = userData;
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [showUserDetails, setShowUserDetails] = useState(false);
  const menuRef = useRef(null);
  const profileButtonRef = useRef(null);
  const { selectedSiteGroup } = useSiteSelector();
  const dispatch = useSiteSelectorDispatch();
  const UserBadge = USER_BADGE[userData.roles?.[0]?.role];

  // Close menu on outside click
  useEffect(() => {
    function handleClickOutside(event) {
      if (menuRef.current && !menuRef.current.contains(event.target)) {
        setIsDropdownOpen(false);
      }
    }

    if (isDropdownOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isDropdownOpen]);

  const siteGroupDropdownOptions = allSites.map((siteGroup) => {
    return {
      id: siteGroup.site_id,
      label:
        siteGroup.tlc +
        ' UTC' +
        DateTime.now().setZone(siteGroup.tz).toFormat('ZZ'),
    };
  });

  const setSelectedSiteGroup = (sg) => {
    dispatch({
      type: SITE_SELECTOR_ACTIONS.SET_SELECTED_SITE_GROUP,
      payload: sg,
    });
  };

  const setSiteGroupFromDropdown = (sg) => {
    if (sg.id) {
      const siteGroup = allSites.find((site) => site.site_id === sg.id);
      setSelectedSiteGroup(siteGroup);
    }
  };

  useEffect(() => {
    // Default to the first site group returned from the API:
    setSelectedSiteGroup(allSites[0]);
  }, [allSites]);

  const handleDropdownClick = () => {
    setIsSideMenuOpen(false);
    setIsDropdownOpen((prevState) => !prevState);
  };

  const handleSubmitLogout = () => {
    const data = { intent: 'logout' };
    submit(data, {
      method: 'post',
      action: '/app',
    });
  };

  const handleAccountDetail = () => {
    setIsDropdownOpen(false);
    setShowUserDetails(true);
  };

  const onCloseUserDetails = () => {
    setShowUserDetails(false);
  };

  const handleMenuClick = () => {
    setIsSideMenuOpen((prevState) => !prevState);
  };

  const getSelectedSiteGroupLabel = () => {
    const selectedSiteGroupOption = allSites.find(
      (option) => option.site_id === selectedSiteGroup?.site_id,
    );
    return `${selectedSiteGroupOption?.tlc} UTC${DateTime.now().setZone(selectedSiteGroupOption?.tz).toFormat('ZZ')}`;
  };

  return (
    <StyledHeader>
      <StyledLeftMobileContainer>
        <StyledHamburgerMenu onClick={handleMenuClick}>
          {!isSideMenuOpen ? <MenuIcon /> : <Close />}
        </StyledHamburgerMenu>
        <LogoMobile />
      </StyledLeftMobileContainer>
      <StyledLeftContainer>
        <LogoHorizontal />
      </StyledLeftContainer>
      <StyledCenterContainer>
        <div className="env-container">
          <MrmLogoIcon />
          <DSSelectDropdown
            options={siteGroupDropdownOptions}
            selectedOption={getSelectedSiteGroupLabel()}
            onChangeSelection={setSiteGroupFromDropdown}
            iconElement={SiteGroupEnvironment}
            condensed
          />
        </div>
      </StyledCenterContainer>
      <StyledRightContainer>
        <DSBadge color="yellow" iconElement={Development}>
          Development Environment
        </DSBadge>
        {UserBadge}
        <StyledProfileButton
          ref={profileButtonRef}
          onClick={handleDropdownClick}
        >
          <StyledNameGroupContainer>{name}</StyledNameGroupContainer>
          <ChevronLeft
            color={theme`colors.dslWhite`}
            rotate={ICON_DIRECTION.DOWN}
          />
        </StyledProfileButton>
        <StyledMenuContainer ref={menuRef} isOpen={isDropdownOpen}>
          <li>
            <StyledMenuButton>
              <DSNavItem
                isExpanded
                label={NAV_LABELS.ACCOUNT_DETAILS}
                onClick={handleAccountDetail}
              />
            </StyledMenuButton>
          </li>
          <li>
            <StyledMenuButton>
              <DSNavItem
                isExpanded
                label={NAV_LABELS.SIGN_OUT}
                onClick={handleSubmitLogout}
              />
            </StyledMenuButton>
          </li>
          <li>
            <StyledTopBorderView>
              <div className="privacy-text">
                <a
                  href="https://www.dreamscapelearn.com/privacy"
                  target="_blank"
                  rel="noreferrer noopener"
                >
                  Privacy Policy
                </a>
              </div>
            </StyledTopBorderView>
          </li>
          <li>
            <StyledVersionDetails>
              <img src={logoSmall} alt="logo-small" />
              <div className="version-details-text">
                DS MRM Version {packageJson.version}
              </div>
            </StyledVersionDetails>
          </li>
        </StyledMenuContainer>
      </StyledRightContainer>
      <DSUserDetails
        isSelfAccount={true}
        showModal={showUserDetails}
        onClose={onCloseUserDetails}
        user={userData}
        userRole={userRoleDropdownOptions.find(
          (role) => role.id === userData.roles?.[0]?.role,
        )}
        loading={false}
        editDisabled
        dropdownOptions={userRoleDropdownOptions}
      />
    </StyledHeader>
  );
};

Header.propTypes = {
  userData: PropTypes.shape({
    name: PropTypes.string,
    role: PropTypes.string,
    first_name: PropTypes.string,
    last_name: PropTypes.string,
    email: PropTypes.string,
  }),
  isSideMenuOpen: PropTypes.bool,
  setIsSideMenuOpen: PropTypes.func,
};

export default Header;
