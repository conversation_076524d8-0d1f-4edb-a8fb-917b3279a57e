import tw from 'twin.macro';
import styled from 'styled-components';
import PropTypes from 'prop-types';

const StyledDSTextAreaContainer = styled.div`
  ${tw`flex flex-col relative transition-all duration-300 ease-in-out w-full`};
`;

const StyledDSTextArea = styled.textarea`
  ${tw`
    transition-all duration-300 ease-in-out
    w-full
    p-2 md:p-4
    text-sm text-dslBlack-2
    bg-transparent
    border border-dslGray-1 rounded-md
    appearance-none
    box-border
    outline-none
    placeholder:(transition-all duration-300 ease-in-out text-dslGray-3)
    focus:border-dslBlue-normal
    resize-none
    disabled:(
      border-dslGray-2 
      bg-dslGray-2
      pointer-events-none
      text-dslGray-5
    )
  `};

  ${({ dark }) =>
    dark
      ? tw`
        text-dslWhite
        border-transparent
        bg-dslBlack-faded-50
        backdrop-blur-lg
      `
      : undefined};

  ${({ error }) =>
    error
      ? tw`border-dslStatus-red placeholder:(transition-all duration-300 ease-in-out text-dslStatus-red)`
      : undefined};
`;

const DSInput = ({ rows = 5, ...props }) => {
  return (
    <StyledDSTextAreaContainer error={props.error} dark={props.dark}>
      <StyledDSTextArea rows={rows} {...props} />
    </StyledDSTextAreaContainer>
  );
};

DSInput.propTypes = {
  /**
   * Label for the input field
   */
  error: PropTypes.bool,
  dark: PropTypes.bool,
  rows: PropTypes.number,
};

export default DSInput;
