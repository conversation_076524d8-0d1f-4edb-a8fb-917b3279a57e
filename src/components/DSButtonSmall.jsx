import tw from 'twin.macro';
import styled from 'styled-components';
import PropTypes from 'prop-types';

const StyledDSButtonSmall = styled.button`
  ${tw`
    transition-all duration-200 ease-in-out
    flex items-center justify-center
    px-2.5 py-2 gap-2
    appearance-none
    outline-none
    box-border
    rounded
    bg-transparent
    border border-dslGray-2
    disabled:(
      pointer-events-none
      opacity-50
    )
    focus-visible:(
      border-dslBlue-dark
      bg-dslBlue-light
    )
  `};

  ${({ outline }) =>
    outline
      ? tw`hover:bg-dslGray-1`
      : tw`hover:(bg-dslBlue-dark border-transparent)`}

  width: ${({ width }) => (width ? `${width}px` : 'auto')};

  .small-btn-label {
    ${tw`
      text-xs
      text-dslBlack-2
      uppercase
      font-bold
    `}
  }
`;

const StyledIconContainer = styled.div`
  svg {
    ${tw`w-5 h-5`};
    path {
      ${tw`transition-all duration-200 ease-in-out focus-visible:fill-dslBlue-dark`};
      ${({ outline }) => (outline ? tw`fill-dslGray-4` : tw`fill-dslWhite`)}

      ${StyledDSButtonSmall}:hover & {
        ${({ outline }) =>
          outline ? tw`fill-dslGray-3` : tw`fill-dslBlue-light`}
      }
    }
  }
`;

const DSButtonSmall = ({ icon: Icon, label, outline, width, ...props }) => {
  return (
    <StyledDSButtonSmall {...props} outline={outline} width={width}>
      {Icon ? (
        <StyledIconContainer outline={outline}>
          <Icon />
        </StyledIconContainer>
      ) : null}
      {label ? <div className="small-btn-label">{label}</div> : null}
    </StyledDSButtonSmall>
  );
};

DSButtonSmall.propTypes = {
  icon: PropTypes.elementType,
  label: PropTypes.string,
  outline: PropTypes.bool,
  width: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
};

export default DSButtonSmall;
