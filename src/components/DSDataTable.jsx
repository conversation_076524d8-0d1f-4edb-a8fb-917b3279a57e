import DataTable from 'react-data-table-component';
import tw from 'twin.macro';

import TableSortIcon from './svg/TableSortIcon';

const customTableStyles = {
  table: {
    style: tw`py-5 md:py-0`,
  },
  headRow: {
    style: tw`px-4 md:px-10 py-0 md:py-5 pt-0 pb-4 gap-[30px] min-h-[40px]`,
  },
  headCells: {
    style: tw`px-0 text-dslGray-4 text-[0.9375rem] text-sm md:text-base font-bold uppercase`,
  },
  rows: {
    style: tw`border-b px-4 md:px-10 py-0 md:py-5 gap-[30px] min-h-[76px] text-base cursor-pointer hover:bg-dslGray-1 active:bg-dslBlue-light disabled:(pointer-events-none cursor-default)`,
  },
  cells: {
    style: tw`px-0 !min-w-[24px] !basis-6`,
  },
};

const DSDataTable = (props) => {
  return (
    <DataTable
      customStyles={customTableStyles}
      sortIcon={<TableSortIcon />}
      {...props}
    />
  );
};

export default DSDataTable;
