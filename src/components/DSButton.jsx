import { forwardRef } from 'react';
import tw from 'twin.macro';
import styled from 'styled-components';
import PropTypes from 'prop-types';

import { DSLoadingSpinner } from '../components';

export const BTN_TYPES = {
  primary: 'primary',
  secondary: 'secondary',
  outline: 'outline',
  danger: 'danger',
};

const StyledDSCommonButton = styled.button`
  ${tw`
    flex items-center justify-center
    px-4 py-3.5
    xs:max-sm:p-2
    md:min-h-[54px] w-[300px]
    text-base font-bold tracking-widest
    appearance-none
    outline-none
    box-border
    rounded
    transition-all duration-300 ease-in-out
    gap-2.5
  `};

  ${({ hug }) => (hug ? tw`w-auto` : undefined)};
  ${({ fullWidth }) => (fullWidth ? tw`w-full` : undefined)};
`;

const StyledDSPrimaryButton = styled(StyledDSCommonButton)`
  ${tw`
    text-dslWhite
    bg-dslBlue-dark
    border border-transparent
    shadow-dslLarge
    hover:(
      bg-dslBlue-darkHover
      text-dslBlue-light
      shadow-dslLargeDescend
    )
    active:(
      text-dslBlue-lightFaded
    )
    disabled:(
      bg-transparent
      border-dslBlue-dark
      text-dslBlue-dark
      shadow-none
      pointer-events-none
    )
    focus-visible:(
      border-dslWhite
    )
  `};
`;

const StyledDSSecondaryButton = styled(StyledDSCommonButton)`
  ${({ dark }) =>
    dark
      ? tw`
          text-dslWhite
          bg-transparent
          border border-dslWhite
          shadow-dslSecondary
          hover:(
            bg-dslBlack-faded-5
          )
          active:(
            opacity-75
          )
          disabled:(
            border border-dslGray-5
            text-dslGray-4
            pointer-events-none
          )`
      : tw`
          text-dslBlack-1
          bg-dslGray-2
          border border-dslGray-3
          shadow-dslSecondary
          hover:(
            bg-dslGray-3
            border-dslGray-4
            text-dslBlack-faded-75
          )
          active:(
            text-dslBlack-faded-25
          )
          disabled:(
            bg-dslGray-1
            border-dslGray-3
            text-dslGray-3
            pointer-events-none
          )
          focus-visible:(
            border-dslGray-4
            bg-dslGray-3
          )
  `};
`;

const StyledDSOutlineButton = styled(StyledDSCommonButton)`
  ${({ selected, dark }) =>
    selected
      ? tw`bg-dslBlue-light
         text-dslBlue-dark
         border
         border-dslBlue-dark
         disabled:(
          bg-dslWhite
          border-dslGray-1
          text-dslGray-3
          pointer-events-none
        )`
      : dark
        ? tw`bg-transparent
        border border-dslWhite
        text-dslWhite
        hover:(
          bg-dslWhite/5
        )
        disabled:(
          pointer-events-none
          opacity-50
        )
      `
        : tw`text-dslBlack-1
         bg-dslWhite
         border border-dslGray-2
         hover:(
           bg-dslGray-1
           border-dslGray-3
           text-dslBlack-faded-75
         )
         active:(
           text-dslBlack-faded-25
         )
         disabled:(
           bg-dslWhite
           border-dslGray-1
           text-dslGray-3
           pointer-events-none
         )
        focus-visible:(
          border-dslBlue-dark
          bg-dslBlue-light
          text-dslBlue-dark
        )
  `};
`;

const StyledDSDangerButton = styled(StyledDSCommonButton)`
  ${tw`
    text-dslWhite
    bg-dslStatus-red
    border border-transparent
    shadow-dslLarge
    hover:(
      text-dslStatus-redLight
      shadow-dslLargeDescend
    )
    disabled:(
      bg-transparent
      border-dslStatus-red
      text-dslStatus-red
      shadow-none
      pointer-events-none
    )
    focus-visible:(
      border-dslWhite
    )
  `};
`;

const BUTTONS = {
  primary: StyledDSPrimaryButton,
  secondary: StyledDSSecondaryButton,
  outline: StyledDSOutlineButton,
  danger: StyledDSDangerButton,
};

const StyledCommonSpan = styled.span`
  ${tw`
    w-5 h-[18px]
    flex justify-center items-center
    transition-all duration-300 ease-in-out
  `};

  ${({ iconAutoWidth }) => (iconAutoWidth ? tw`w-auto` : undefined)};

  svg {
    path {
      ${tw`transition-all duration-300 ease-in-out`};
    }
  }
`;

const StyledSelectedSpan = styled(StyledCommonSpan)`
  svg {
    path {
      ${tw`fill-dslBlue-dark`};
      ${StyledDSCommonButton}:disabled & {
        ${tw`fill-dslGray-3`};
      }
    }
  }
`;

const StyledPrimarySpan = styled(StyledCommonSpan)`
  svg {
    path {
      ${tw`fill-dslBlue-light`};

      ${StyledDSCommonButton}:hover & {
        ${tw`fill-dslBlue-light`};
      }
      ${StyledDSCommonButton}:active & {
        ${tw`fill-dslBlue-lightFaded`};
      }
      ${StyledDSCommonButton}:disabled & {
        ${tw`fill-dslBlue-dark`};
      }
    }
  }
`;

const StyledSecondarySpan = styled(StyledCommonSpan)`
  svg {
    path {
      ${tw`fill-dslBlack-1`};

      ${StyledDSCommonButton}:hover & {
        ${tw`fill-dslBlack-faded-75`};
      }
      ${StyledDSCommonButton}:active & {
        ${tw`fill-dslBlack-faded-25`};
      }
      ${StyledDSCommonButton}:disabled & {
        ${tw`fill-dslGray-3`};
      }
      ${StyledDSCommonButton}:focus-visible & {
        ${tw`fill-dslGray-4`};
      }
      ${({ dark }) => (!dark ? undefined : tw`fill-dslBlack-1`)};
    }
  }
`;

// Maintain Outline separate from Secondary in case their styles diverge
const StyledOutlineSpan = styled(StyledCommonSpan)`
  svg {
    path {
      ${({ dark }) => (dark ? tw`fill-dslWhite` : tw`fill-dslGray-4`)};

      ${StyledDSCommonButton}:active & {
        ${({ dark }) => (dark ? tw`fill-dslWhite` : tw`fill-dslGray-3`)};
      }

      ${StyledDSCommonButton}:disabled & {
        ${tw`opacity-50`}
      }

      ${StyledDSCommonButton}:focus-visible & {
        ${tw`fill-dslBlue-dark`}
    }
  }
`;

const StyledDangerSpan = styled(StyledCommonSpan)`
  svg {
    path {
      ${tw`fill-dslStatus-redLight`};

      ${StyledDSCommonButton}:hover & {
        ${tw`fill-dslStatus-redLight`};
      }
      ${StyledDSCommonButton}:active & {
        ${tw`opacity-50`};
      }
      ${StyledDSCommonButton}:disabled & {
        ${tw`fill-dslStatus-red`};
      }
    }
  }
`;

const reconcileIconContainer = (variant, selected, dark) => {
  if (variant === BTN_TYPES.outline && selected) {
    return StyledSelectedSpan;
  }

  if (variant === BTN_TYPES.secondary && !dark) {
    return StyledCommonSpan;
  }

  switch (variant) {
    case BTN_TYPES.primary:
      return StyledPrimarySpan;
    case BTN_TYPES.secondary:
      return StyledSecondarySpan;
    case BTN_TYPES.outline:
      return StyledOutlineSpan;
    case BTN_TYPES.danger:
      return StyledDangerSpan;
    default:
      return null;
  }
};

const DSButton = forwardRef(
  (
    {
      label,
      icon,
      rightIcon,
      variant = BTN_TYPES.primary,
      selected,
      dark,
      iconAutoWidth,
      loading,
      disabled,
      ...props
    },
    ref,
  ) => {
    const StyledDSButton = BUTTONS[variant];
    const IconContainer = reconcileIconContainer(variant, selected, dark);
    return (
      <StyledDSButton
        ref={ref}
        selected={selected}
        dark={dark}
        disabled={loading || disabled}
        {...props}
      >
        {icon && !loading ? (
          <IconContainer dark={dark} iconAutoWidth={iconAutoWidth}>
            {icon}
          </IconContainer>
        ) : null}
        {loading ? (
          <IconContainer iconAutoWidth={iconAutoWidth} dark={dark}>
            <DSLoadingSpinner />
          </IconContainer>
        ) : (
          label?.toUpperCase() || ''
        )}
        {rightIcon && !loading ? (
          <IconContainer iconAutoWidth={iconAutoWidth} dark={dark}>
            {rightIcon}
          </IconContainer>
        ) : null}
      </StyledDSButton>
    );
  },
);

DSButton.displayName = 'DSButton';
DSButton.propTypes = {
  label: PropTypes.string,
  icon: PropTypes.object,
  rightIcon: PropTypes.object,
  variant: PropTypes.oneOf(Object.keys(BTN_TYPES)),
  fullWidth: PropTypes.bool,
  hug: PropTypes.bool,
  disabled: PropTypes.bool,
  selected: PropTypes.bool,
  dark: PropTypes.bool,
  iconAutoWidth: PropTypes.bool,
  loading: PropTypes.bool,
  key: PropTypes.string,
};

export default DSButton;
