import { forwardRef } from 'react';
import styled from 'styled-components';
import tw from 'twin.macro';

import checkIconRaw from '../assets/check-icon.svg?raw';
const checkIconEncoded = encodeURIComponent(checkIconRaw);

const StyledInputCheckbox = styled.input`
  &[type='checkbox'] {
    ::before {
      background-image: url('data:image/svg+xml,${checkIconEncoded}');
      background-size: contain;
    }

    ${tw`
      transition-all duration-150 ease-in-out
      appearance-none
      w-6 h-6 aspect-square
      rounded
      box-border
      border border-dslGray-2
      cursor-pointer
      hover:border-dslGray-3
      checked:(border-transparent bg-dslBlue-dark)
      checked:hover:border-transparent
      checked:before:opacity-100
      before:(
        bg-no-repeat bg-center bg-[length:14px]
        inline-block
        h-full w-full
        content-['']
        opacity-0
      )
      disabled:(pointer-events-none opacity-50)
    `};
  }
`;

const DSCheckbox = forwardRef((props, ref) => {
  return <StyledInputCheckbox type="checkbox" ref={ref} {...props} readOnly />;
});

DSCheckbox.displayName = 'DSCheckbox';

export default DSCheckbox;
