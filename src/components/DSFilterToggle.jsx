import { forwardRef } from 'react';
import PropTypes from 'prop-types';
import styled from 'styled-components';
import tw from 'twin.macro';

import DSButton from './DSButton';
import DSCheckbox from './DSCheckbox';
import { FilterList } from './svg/icons';

export const ALIGN_OPTIONS = {
  LEFT: 'left',
  RIGHT: 'right',
};

const StyledFilterToggle = styled.div`
  ${tw`
    inline-flex
    relative
  `}

  .filters-count {
    ${tw`
      absolute
      top-[-10px]
      right-[-5px]
      bg-dslBlue-dark
      text-dslWhite
      text-[10px]
      font-bold
      flex
      items-center
      justify-center
      rounded-full
      w-[18px]
      h-[18px]
      leading-none
    `}
  }

  .filter-toggle-cont {
    ${tw`
      absolute
      top-[54px]
      w-[226px]
      h-[420px]
      rounded-[3px]
      shadow-dslMedium
      border
      border-solid
      border-dslGray-1
      bg-dslWhite
      mt-[10px]
      overflow-hidden
      transition
      duration-300
      ease-in-out
      z-40
    `}

    ${(props) => (props.open ? tw`flex flex-col` : tw`hidden`)}
    ${(props) =>
      props.align === ALIGN_OPTIONS.RIGHT ? tw`right-0` : tw`left-0`}

    .filter-toggle-header {
      ${tw`
        flex
        items-center
        justify-between
        px-[24px]
        py-[20px]
        border-b
        border-solid
        border-dslGray-2
      `}

      .filter-toggle-header-left {
        ${tw`
          text-[16px]
          font-bold
          tracking-[0.24px]
          uppercase
        `}
      }

      .filter-toggle-header-right {
        ${tw`
          text-[16px]
          text-dslBlue-dark
          leading-[25px]
        `}
      }
    }

    .filter-list-items {
      ${tw`
        px-[24px]
        py-[20px]
        h-full
        overflow-y-auto
      `}
    }
  }
`;

const StyledDSFilterSection = styled.div`
  .filter-list-header {
    ${tw`mb-[16px]`}

    ${({ disabled }) => (disabled ? tw`opacity-50` : tw``)}
  }
`;

const StyledDSFilterItem = styled.div`
  .filter-list-item {
    ${tw`
      flex
      items-end
      mb-[16px]          
    `}

    label {
      ${tw`
        ml-[10px]
      `}
    }

    ${({ disabled }) => (disabled ? tw`opacity-50 pointer-events-none` : tw``)}
  }
`;

const DSFilterToggle = forwardRef(
  (
    {
      open,
      onClick,
      options,
      selectedFilters,
      onToggleFilter,
      onClearAllFilters,
      clearButtonText,
      align,
    },
    ref,
  ) => {
    return (
      <StyledFilterToggle ref={ref} open={open} align={align}>
        <div>
          <DSButton
            selected={open}
            variant="outline"
            icon={<FilterList />}
            onClick={onClick}
            hug
          />
          {selectedFilters.length > 0 && (
            <div
              className="filters-count"
              aria-label={`${selectedFilters.length} filter selected`}
            >
              {selectedFilters.length}
            </div>
          )}
        </div>
        <div className="filter-toggle-cont">
          <div className="filter-toggle-header">
            <div className="filter-toggle-header-left">filters</div>
            <button
              className="filter-toggle-header-right"
              onClick={onClearAllFilters}
            >
              {clearButtonText ?? 'Clear All'}
            </button>
          </div>
          <div className="filter-list-items">
            {Object.entries(options).map(([section, items]) => {
              return (
                <StyledDSFilterSection key={section} disabled={items.disabled}>
                  <div className="filter-list-header">
                    {section.split('_').join(' ')}
                  </div>
                  {items.options.map((item) => {
                    return (
                      <StyledDSFilterItem key={item} disabled={items.disabled}>
                        <div className="filter-list-item">
                          <DSCheckbox
                            name={section}
                            value={item}
                            onClick={onToggleFilter}
                            checked={selectedFilters.includes(item)}
                            disabled={items.disabled}
                          />
                          <label htmlFor={item}>{item}</label>
                        </div>
                      </StyledDSFilterItem>
                    );
                  })}
                </StyledDSFilterSection>
              );
            })}
          </div>
        </div>
      </StyledFilterToggle>
    );
  },
);

DSFilterToggle.displayName = 'DSFilterToggle';

DSFilterToggle.propTypes = {
  open: PropTypes.bool,
  options: PropTypes.object,
  selectedFilters: PropTypes.array,
  align: PropTypes.oneOf(Object.values(ALIGN_OPTIONS)),
  onClick: PropTypes.func,
  onToggleFilter: PropTypes.func,
  onClearAllFilters: PropTypes.func,
  clearButtonText: PropTypes.string,
};

export default DSFilterToggle;
