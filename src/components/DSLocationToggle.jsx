import { forwardRef } from 'react';
import PropTypes from 'prop-types';
import styled from 'styled-components';
import tw from 'twin.macro';

import DSButton from './DSButton';
import DSCheckbox from './DSCheckbox';
import { ChevronDown } from './svg/icons';

export const ALIGN_OPTIONS = {
  LEFT: 'left',
  RIGHT: 'right',
};

const StyledLocationToggle = styled.div`
  ${tw`
    inline-flex
    relative
  `}
  .location-count {
    ${tw`
      absolute
      top-[-10px]
      right-[-5px]
      bg-dslBlue-dark
      text-dslWhite
      text-[10px]
      font-bold
      flex
      items-center
      justify-center
      rounded-full
      w-[18px]
      h-[18px]
      leading-none
    `}
  }
  .location-toggle-cont {
    ${tw`
      absolute
      top-[54px]
      w-[226px]
      h-[420px]
      rounded-[3px]
      shadow-dslMedium
      border
      border-solid
      border-dslGray-1
      bg-dslWhite
      mt-[10px]
      overflow-hidden
      transition
      duration-300
      ease-in-out
      z-40
    `}

    ${(props) => (props.open ? tw`flex flex-col` : tw`hidden`)}
    ${(props) =>
      props.align === ALIGN_OPTIONS.RIGHT ? tw`right-0` : tw`left-0`}

    .location-toggle-header {
      ${tw`
        flex
        items-center
        justify-between
        px-[24px]
        py-[20px]
        border-b
        border-solid
        border-dslGray-2
      `}

      .location-toggle-header-left {
        ${tw`
          text-[16px]
          font-bold
          tracking-[0.24px]
          uppercase
        `}
      }

      .location-toggle-header-right {
        ${tw`
          text-[16px]
          text-dslBlue-dark
          leading-[25px]
          hover:underline
          cursor-pointer
        `}
      }
    }

    .location-list-items {
      ${tw`
        px-[24px]
        py-[20px]
        h-full
        overflow-y-auto
      `}
    }
  }
`;

const StyledDSLocationSection = styled.div`
  .location-list-header {
    ${tw`mb-[16px]`}

    ${({ disabled }) => (disabled ? tw`opacity-50` : tw``)}
  }
`;

const StyledDSLocationItem = styled.div`
  .location-list-item {
    ${tw`
      flex
      items-end
      mb-[16px]          
    `}

    label {
      ${tw`
        ml-[10px]
        text-base
        text-dslBlack-2
      `}
    }

    ${({ disabled }) => (disabled ? tw`opacity-50 pointer-events-none` : tw``)}
  }
`;

const DSLocationToggle = forwardRef(
  (
    {
      open,
      onClick,
      options,
      selectedLocations,
      onToggleLocation,
      onClearAllLocations,
      clearButtonText,
      align,
    },
    ref,
  ) => {
    const optionsData = options.map((location) => ({
      value: location.site_group_location_id,
      label: location.name,
    }));

    return (
      <StyledLocationToggle ref={ref} open={open} align={align}>
        <div>
          <DSButton
            selected={open}
            variant="outline"
            onClick={onClick}
            hug
            label="Locations"
            rightIcon={<ChevronDown />}
            aria-label={`Toggle location filter, ${selectedLocations.length} selected`}
          />
          {selectedLocations.length > 0 && (
            <div
              className="location-count"
              aria-label={`${selectedLocations.length} locations selected`}
            >
              {selectedLocations.length}
            </div>
          )}
        </div>
        <div className="location-toggle-cont">
          <div className="location-toggle-header">
            <div className="location-toggle-header-left">Locations</div>
            <button
              className="location-toggle-header-right"
              onClick={onClearAllLocations}
              aria-label="Clear all selected locations"
            >
              {clearButtonText ?? 'Clear All'}
            </button>
          </div>
          <div className="location-list-items">
            <StyledDSLocationSection>
              {optionsData &&
                optionsData.map((item) => {
                  const id = `location-${item.value}`;
                  return (
                    <StyledDSLocationItem key={id}>
                      <div className="location-list-item">
                        <DSCheckbox
                          id={id}
                          name={item.label}
                          value={item.value}
                          onChange={onToggleLocation}
                          checked={selectedLocations.includes(item.value)}
                          aria-label={`Select ${item.label}`}
                        />
                        <label htmlFor={id}>{item.label}</label>
                      </div>
                    </StyledDSLocationItem>
                  );
                })}
            </StyledDSLocationSection>
          </div>
        </div>
      </StyledLocationToggle>
    );
  },
);

DSLocationToggle.displayName = 'DSLocationToggle';

DSLocationToggle.propTypes = {
  open: PropTypes.bool,
  options: PropTypes.arrayOf(
    PropTypes.shape({
      value: PropTypes.string.isRequired,
      label: PropTypes.string.isRequired,
    }),
  ),
  selectedLocations: PropTypes.arrayOf(PropTypes.string),
  align: PropTypes.oneOf(Object.values(ALIGN_OPTIONS)),
  onClick: PropTypes.func,
  onToggleLocation: PropTypes.func,
  onClearAllLocations: PropTypes.func,
  clearButtonText: PropTypes.string,
};

DSLocationToggle.defaultProps = {
  open: false,
  options: [],
  selectedLocations: [],
  align: ALIGN_OPTIONS.RIGHT,
};

export default DSLocationToggle;
