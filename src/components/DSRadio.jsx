import { forwardRef } from 'react';
import PropTypes from 'prop-types';
import styled from 'styled-components';
import tw from 'twin.macro';

import checkIconRaw from '../assets/check-icon.svg?raw';
const checkIconEncoded = encodeURIComponent(checkIconRaw);

const StyledInputRadio = styled.input`
  ::before {
    background-image: url('data:image/svg+xml,${checkIconEncoded}');
  }

  ${tw`
    transition-all duration-150 ease-in-out
    appearance-none
    w-6 h-6
    rounded-full
    box-border
    border border-dslGray-2
    cursor-pointer
    hover:border-dslGray-3
    checked:(border-transparent bg-dslBlue-dark)
    checked:hover:border-transparent
    checked:before:opacity-100
    before:(
      bg-no-repeat bg-center bg-[length:14px]
      inline-block
      h-full w-full
      content-['']
      opacity-0
    )
    disabled:pointer-events-none
  `};
`;

const DSRadio = forwardRef(({ type, ...props }, ref) => {
  return <StyledInputRadio type={type || 'radio'} ref={ref} {...props} />;
});

DSRadio.displayName = 'DSRadio';
DSRadio.propTypes = {
  type: PropTypes.string,
};

export default DSRadio;
