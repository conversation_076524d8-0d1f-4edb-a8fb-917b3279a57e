import React from 'react';
import PropTypes from 'prop-types';
import { DateTime } from 'luxon';
import styled from 'styled-components';
import tw from 'twin.macro';

const EventContainer = styled.div`
  ${tw`
    px-2.5
    py-[6px]
    rounded
    overflow-hidden
    whitespace-nowrap
    text-xs
    text-white
    text-ellipsis
    leading-none
    align-middle
    tracking-[0px]
    max-w-full
  `}
`;

const TimeSpan = styled.span`
  ${tw`font-normal`}
`;

const AddressDiv = styled.div`
  ${tw`
    font-bold
    overflow-hidden
    text-ellipsis
    whitespace-nowrap
  `}
`;

const DSEvent = ({ arg, timezone }) => {
  const { event } = arg;
  const { start, end, title } = event;
  const startTime = DateTime.fromJSDate(start).setZone(timezone);
  const endTime = DateTime.fromJSDate(end).setZone(timezone);
  const formattedTime = `(${startTime.toFormat('ccc h:mma')} - ${endTime.toFormat('ccc h:mma')} ${startTime.zoneName})`;

  return (
    <EventContainer>
      {title && (
        <AddressDiv>
          {title}
          <TimeSpan>{' ' + formattedTime}</TimeSpan>
        </AddressDiv>
      )}
    </EventContainer>
  );
};

DSEvent.propTypes = {
  arg: PropTypes.shape({
    event: PropTypes.shape({
      start: PropTypes.instanceOf(Date).isRequired,
      end: PropTypes.instanceOf(Date).isRequired,
      title: PropTypes.string.isRequired,
      extendedProps: PropTypes.object.isRequired,
    }).isRequired,
  }).isRequired,
  timezone: PropTypes.string.isRequired,
};

export default DSEvent;
