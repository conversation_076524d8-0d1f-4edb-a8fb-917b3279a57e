import { useState, useRef } from 'react';
import PropTypes from 'prop-types';
import styled from 'styled-components';
import tw from 'twin.macro';

import { VerticalMenu } from './svg/icons';

import { useOutsideClicked } from '../hooks/index.mjs';

const StyledDSOptionsMenu = styled.div`
  ${tw`
    relative
  `}

  .btn-div {
    ${tw`flex items-center justify-center rounded w-10 h-10`}
  }
  .active-btn {
    ${tw`bg-dslBlue-dark`}

    svg {
      path {
        ${tw`fill-dslBlue-light`};
      }
    }
  }

  .deactive-btn {
    ${tw`bg-dslWhite`}

    svg {
      path {
        ${tw`fill-dslGray-4`};
      }
    }
  }
`;

const StyledOptionsMenuItems = styled.div`
  ${tw`
    absolute
    right-0
    top-14
    w-56
    rounded-[3px]
    shadow-dslMedium
    bg-dslWhite
    border
    border-solid
    border-dslGray-2
    px-1
    overflow-y-auto
    max-h-[266px]
    mt-1
    z-20
  `}
`;

const StyledMenuItem = styled.button`
  ${tw`
    flex
    items-center
    w-full
    p-2.5
    mt-[4px]
    text-[14px]
    rounded-[3px]
    hover:bg-dslGray-2
    active:bg-dslBlue-light
    last:mb-[4px]
  `}

  &:disabled {
    ${tw`cursor-not-allowed opacity-50 pointer-events-none`}
  }

  .dropdown-item-text {
    ${tw`text-sm`}
  }
`;

const StyledMenuItemIcon = styled.div`
  ${tw`
    flex
    items-center
    w-6
    h-6
  `}

  ${(props) => (props.isItem ? tw`mr-[10px]` : tw`mr-[5px]`)}

  svg {
    path {
      ${tw`fill-dslGray-4`}
    }
  }
`;

const DSOptionMenu = ({ options, onSelectOption, disabled }) => {
  const menuRef = useRef(null);
  const [open, setOpen] = useState(false);
  useOutsideClicked(menuRef, () => setOpen(false));

  const handleChangeSelection = (option) => {
    onSelectOption(option);
    setOpen(false);
  };

  return (
    <StyledDSOptionsMenu ref={menuRef}>
      <div
        className={`btn-div ${open ? 'active-btn' : 'deactive-btn'}`}
        disabled={disabled}
        onClick={() => {
          disabled ? {} : setOpen(!open);
        }}
      >
        <VerticalMenu width={20} height={20} />
      </div>
      {open && (
        <StyledOptionsMenuItems>
          {options.map((option) => {
            const ItemIcon = option.icon;
            return (
              <StyledMenuItem
                key={option?.id}
                onClick={() => handleChangeSelection(option)}
                disabled={option.isDisable}
              >
                {ItemIcon && (
                  <StyledMenuItemIcon isItem>
                    <ItemIcon />
                  </StyledMenuItemIcon>
                )}
                <div className="dropdown-item-text">{option?.label}</div>
              </StyledMenuItem>
            );
          })}
        </StyledOptionsMenuItems>
      )}
    </StyledDSOptionsMenu>
  );
};

DSOptionMenu.propTypes = {
  icon: PropTypes.elementType,
  disabled: PropTypes.bool,
  options: PropTypes.arrayOf(
    PropTypes.shape({
      id: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
      label: PropTypes.string,
      icon: PropTypes.elementType,
      isDisable: PropTypes.bool,
    }),
  ),
  onSelectOption: PropTypes.func,
};

export default DSOptionMenu;
