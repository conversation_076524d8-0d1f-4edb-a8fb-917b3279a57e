import PropTypes from 'prop-types';
import styled from 'styled-components';
import tw from 'twin.macro';
import { DateTime } from 'luxon';
import { useRef } from 'react';

import { DSIconButton } from '../components';
import {
  Pencil,
  Trash,
  Close,
  World,
  Description,
} from '../components/svg/icons';

import { useOutsideClicked } from '../hooks/index.mjs';
import { CircleUnion } from './svg/icons';
import CopyIcon from './svg/icons/small/CopyIcon';

const StyledQuickViewContainer = styled.div`
  ${tw`
    absolute
    z-30
    top-[50%]
    left-1/4
    flex flex-col
    rounded
    w-[430px]
    border border-dslGray-1
    bg-dslWhite
    shadow-dslMedium
  `}

  .event-qv-section {
    ${tw`
      pt-5
      pb-0
    `};
    .event-qv-sub-section {
      ${tw`
      px-6
    `};
    }
  }

  .event-address-section {
    ${tw`
      py-5 px-6
    `};
    .event-address-row {
      ${tw`
        flex flex-row
        items-center
        gap-2.5
      `};
    }
    .location-type {
      ${tw`
        text-[16px]
        leading-[24px]
        text-dslWhite
        bg-[#2E3191]
        px-2 py-1
        rounded-md
      `};
    }
    .add-type {
      ${tw`
        text-[11px]
        leading-[17px]
        text-dslGray-4
        font-normal
        my-2
      `};
    }
    .add-value {
      ${tw`
        text-[13px]
        leading-[20px]
        text-dslBlack-2
        font-normal
        
      `};
    }
  }

  .event-qv-info-section {
    ${tw`
      border-b-[1px] border-b-dslGray-2
    `};

    .qv-icon-buttons {
      ${tw`
        flex flex-row
        items-center justify-end
        gap-2.5
      `};
    }
    .event-resources {
      ${tw`
        flex flex-row
        items-center
        gap-2.5
      `};
      .event-resources-title {
        ${tw`
          text-[25px]
          leading-[37px]
          text-dslBlack-2
          font-bold
        `}
      }
    }

    .event-time-secton {
      ${tw`
        flex flex-row
        items-center
        justify-evenly
        my-4
      `}
      .time-label {
        ${tw`
        text-[11px]
        leading-[17px]
        text-dslBlack-2
        font-normal        
      `}
      }
      .time-value {
        ${tw`
        text-[16px]
        leading-[26px]
        text-dslBlack-2
        font-bold
        uppercase
      `}
      }
    }
  }
`;

const exampleEvent = {
  session_id: 'dsl-ASU00001-a2de2602-1d15-4d9c-9d68-08da3fcd6a65',
  start_timestamp: '2023-10-06T18:20:00+00:00',
  title_id: 'alienzoolearnm02r0101',
  pod_id: 'ASU00001-03',
  end_timestamp: '2023-10-06T18:40:00+00:00',
  seats_available: 12,
  gearup_id: 'ASU00001-03-A',
  max_seats: 12,
  seats_reserved: 0,
  ada_reserved: 0,
  status_code: 1110,
  ticket_partner_session_id:
    'dsl-ASU00001-a2de2602-1d15-4d9c-9d68-08da3fcd6a65',
  ticket_partner_id: 'dsl',
  site_id: 'ASU00001',
  sync_timestamp: '2023-08-08T21:40:01.222768+00:00',
  show_type: 1620,
  notification_latch_timestamp: null,
  shared_resource_id:
    'alienzoolearnm02r0101_ASU00001-03-A_2023-10-06_1120-1620',
  show_time_description: 'hidden show',
  public_sale: false,
  public_signage: false,
  guests_seen: 0,
  status_description: 'pending',
  title_name: 'Cell Biology Act 1',
  latest_session_update: null,
  pos_sold_out: false,
  pos_cancelled: false,
};

const DSEventQuickView = ({
  event = exampleEvent,
  onClose,
  onDiscard,
  onDelete,
  onEdit,
  xPos,
  yPos,
  timezone,
  onClearReservations,
  ...props
}) => {
  const {
    end_timestamp,
    start_timestamp,
    resourceId,
    address,
    location_info,
    notes,
    backgroundColor,
  } = event;

  const startDateTime = DateTime.fromISO(start_timestamp, { zone: timezone });
  const startDate = startDateTime.toFormat('EEE, MMMM d h:mma z');
  const endDateTime = DateTime.fromISO(end_timestamp, { zone: timezone });
  const endDate = endDateTime.toFormat('EEE, MMMM d h:mma z');
  const sessionOver = startDateTime < DateTime.local({ zone: timezone });

  const wrapperRef = useRef(null);
  useOutsideClicked(wrapperRef, onClose);

  return (
    <StyledQuickViewContainer
      ref={wrapperRef}
      {...props}
      style={{ top: yPos - 50, left: xPos > 450 ? xPos : 450 }}
    >
      <div className="event-qv-section event-qv-info-section">
        <div className="event-qv-sub-section">
          <div className="qv-icon-buttons">
            <DSIconButton
              icon={Pencil}
              onClick={() => onEdit(event)}
              disabled={!event.is_draft}
            />
            <DSIconButton icon={Trash} onClick={onDelete} />
            <DSIconButton icon={Close} onClick={onClose} />
          </div>
          <div className="event-resources">
            <CircleUnion />
            <div className="event-resources-title">{resourceId}</div>
          </div>
          <div className="event-time-secton">
            <div>
              <div className="time-label">Start Time</div>
              <div className="time-value">{startDate}</div>
            </div>
            <div>
              <div className="time-label">End Time</div>
              <div className="time-value">{endDate}</div>
            </div>
          </div>
        </div>
      </div>
      <div className="event-address-section">
        {/* Address & Location Notes */}
        <div className="event-address-row">
          <div>
            <World width="30px" height="30px" />
          </div>
          <div>
            <span
              className="location-type"
              style={{ backgroundColor: backgroundColor }}
            >
              {event?.title_name}
            </span>
            <div>
              <div className="add-type">Address</div>
              <div className="flex items-center">
                <div className="add-value flex items-center gap-2">
                  <span>{address}</span>
                  <CopyIcon
                    style={{
                      display: 'inline-block',
                      verticalAlign: 'middle',
                      marginLeft: '8px',
                      marginTop: '-4px',
                    }}
                    onClick={() => navigator.clipboard.writeText(address)}
                  />
                </div>
              </div>
            </div>
            <div>
              <div className="add-type">Location Notes</div>
              <div className="add-value">{location_info}</div>
            </div>
          </div>
        </div>
        <div className="event-address-row">
          <div>
            <Description width="30px" height="30px" />
          </div>
          <div>
            <div className="add-type">Driver Notes</div>
            <div className="add-value">{notes}</div>
          </div>
        </div>
      </div>
    </StyledQuickViewContainer>
  );
};

DSEventQuickView.propTypes = {
  event: PropTypes.object,
  onClose: PropTypes.func,
  onDiscard: PropTypes.func,
  onDelete: PropTypes.func,
  onEdit: PropTypes.func,
  xPos: PropTypes.number,
  yPos: PropTypes.number,
  timezone: PropTypes.string,
  onClearReservations: PropTypes.func,
};

export default DSEventQuickView;
