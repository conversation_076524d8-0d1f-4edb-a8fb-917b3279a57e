import { createPortal } from 'react-dom';
import PropTypes from 'prop-types';
import styled from 'styled-components';
import tw from 'twin.macro';

import { DSBadge, DSButton } from './';
import { Close, ChevronLeft } from './svg/icons';
import { USER_ROLES } from '../utils/constants';

const StyledPopoverOverlay = styled.div`
  ${tw`
    flex items-center justify-center
    absolute top-0 left-0
    z-50
    w-full
    h-full
    bg-dslBlack-faded-50
    overflow-y-hidden
  `};
`;

const StyledPopoverContainer = styled.div`
  ${tw`
    flex flex-col
    bg-dslWhite
    md:rounded
    max-w-[55.25rem] w-full
    relative
    overflow-y-auto
  `};
  ${({ hug }) =>
    hug ? tw`md:h-auto xs:max-sm:h-full` : tw`md:h-5/6 xs:max-sm:h-full`}
`;

const StyledPopoverHeader = styled.div`
  ${tw`
    flex justify-between
    px-4 sm:px-5 md:px-10
    pt-12 sm:pt-14
    pb-4 sm:pb-6
    md:py-[50px]
    py-7.5
    bg-dslBlack-2
    text-dslWhite
    md:rounded-t
    z-0
    gap-1

    // Support for fixed popover header on mobile
    xs:max-sm:top-0
    xs:max-sm:w-full
    xs:max-sm:z-20
  `};

  .navigation-cont {
    ${tw`flex`}
  }

  h1 {
    ${tw`
      // Using a fixed width here so that we can truncate long text in the header
      text-lg
      sm:text-2xl
      md:text-3xl
      font-bold
      uppercase
      max-w-[240px]
      sm:max-w-[400px]
      md:max-w-[600px]
      truncate
      text-wrap
    `};
  }

  h4 {
    ${tw`
      text-xs
      sm:text-sm
      text-dslGray-2
      font-bold
      uppercase
      tracking-[0.015em]
    `};
  }

  button {
    ${tw`
      w-8 sm:w-10 md:w-14
      h-8 sm:h-10 md:h-14
      -ml-1
      sm:ml-0
    `}
  }
`;

const StyledPopoverBody = styled.div`
  ${tw`
    flex flex-col
    flex-1
    h-full
  `};

  ${({ scrollable }) => (scrollable ? tw`overflow-y-auto` : tw`overflow-y-hidden`)}

  ${({ hug }) => (hug ? tw`h-auto` : tw`xs:max-sm:h-auto md:h-[50.25rem]`)}

  .text-above-footer {
  ${tw`
    mt-auto
    mx-5
    mb-6
  `};
  `;

const StyledToastMessageContainer = styled.div`
  ${tw`
    absolute
    bottom-56
    md:left-0 md:right-0
    left-4 right-4
    z-10
    w-fit
    p-3
  `};
  margin-inline: auto;
`;

const StyledPopoverFooter = styled.div`
  ${tw`
    flex
    flex-col md:flex-row
    items-center
    justify-end
    max-h-64
    w-full
    px-5 md:px-10
    py-5 md:py-[30px]
    border-t border-dslGray-2
    gap-3
    md:gap-8
    bottom-0
    bg-dslWhite
    z-1
  `};

  button {
    ${tw`
      w-full md:w-[300px]
    `}
  }
`;

const StyledVerticalDivider = styled.div`
  ${tw`
    hidden
    md:block
    w-[1px] h-12
    bg-dslGray-2
  `};
`;

const StyledFooterLabel = styled.div`
  ${tw`
    text-sm
    md:text-base
    font-bold
    uppercase
    md:tracking-[0.015em]
    text-center md:text-right
  `};
`;

const USER_BADGE = {
  [USER_ROLES.SUPER_ADMIN]: <DSBadge color="gradient">Super Admin</DSBadge>,
  [USER_ROLES.ADMIN]: <DSBadge color="primary">Admin</DSBadge>,
  [USER_ROLES.SCHEDULER]: <DSBadge color="green">District Scheduler</DSBadge>,
  [USER_ROLES.DRIVER]: <DSBadge color="brown">Driver</DSBadge>,
};

const Popover = ({
  userData,
  showModal,
  onClose,
  footerInfo,
  footerButton,
  footerElement,
  showFooter = true,
  showHeader = true,
  content,
  heading,
  subHeading,
  headerButtons,
  goBack,
  showBackButton,
  navigationDisabled,
  hug,
  renderToastMessage,
  showToastMessage,
  scrollable = true,
  bottomContent,
}) => {
  if (!showModal) {
    return null;
  }

  return createPortal(
    <StyledPopoverOverlay>
      <StyledPopoverContainer>
        {showHeader && (
          <StyledPopoverHeader>
            <div className="navigation-cont" tw="gap-3">
              {showBackButton && (
                <DSButton
                  icon={<ChevronLeft />}
                  onClick={goBack}
                  disabled={navigationDisabled}
                  variant="outline"
                  dark
                />
              )}
              <div>
                <h4>{subHeading}</h4>
                <h1>{heading}</h1>
                <div tw="mt-1">{USER_BADGE[userData?.role]}</div>
              </div>
            </div>
            <div className="navigation-cont">
              {headerButtons}
              <DSButton
                icon={<Close />}
                onClick={onClose}
                disabled={navigationDisabled}
                variant="outline"
                dark
              />
            </div>
          </StyledPopoverHeader>
        )}
        <StyledPopoverBody scrollable={scrollable} hug={hug}>
          {content}

          {renderToastMessage && showToastMessage && (
            <StyledToastMessageContainer>
              {renderToastMessage}
            </StyledToastMessageContainer>
          )}

          {bottomContent && (
            <div className="text-above-footer">{bottomContent}</div>
          )}
        </StyledPopoverBody>

        {showFooter && (
          <StyledPopoverFooter>
            {footerInfo && (
              <>
                <StyledFooterLabel>{footerInfo}</StyledFooterLabel>
                <StyledVerticalDivider />
              </>
            )}
            {footerElement}
            {footerButton}
          </StyledPopoverFooter>
        )}
      </StyledPopoverContainer>
    </StyledPopoverOverlay>,
    document.body,
  );
};

Popover.propTypes = {
  userData: PropTypes.shape({
    name: PropTypes.string,
    role: PropTypes.string,
  }),
  showModal: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  footerInfo: PropTypes.string,
  footerButton: PropTypes.element,
  footerElement: PropTypes.element,
  showFooter: PropTypes.bool,
  showHeader: PropTypes.bool,
  content: PropTypes.element,
  heading: PropTypes.string,
  subHeading: PropTypes.string,
  headerButtons: PropTypes.arrayOf(PropTypes.node),
  goBack: PropTypes.func,
  showBackButton: PropTypes.bool,
  navigationDisabled: PropTypes.bool,
  hug: PropTypes.bool,
  scrollable: PropTypes.bool,
  showToastMessage: PropTypes.bool,
  renderToastMessage: PropTypes.element,
  bottomContent: PropTypes.element,
};

export default Popover;
