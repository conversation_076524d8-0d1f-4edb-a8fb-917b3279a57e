import PropTypes from 'prop-types';
import styled from 'styled-components';
import tw from 'twin.macro';
import { Refresh } from '../components/svg/icons/small';

const StyledRefreshButton = styled.button`
  ${tw`
    flex
    items-center
    px-1.5
    py-0.5
    border border-dslGray-2
    hover:(
      border-dslGray-3
      bg-dslGray-2
    )
    rounded-[3px]
    transition-all
    duration-300
    ease-in-out
  `}

  .refresh-text {
    ${tw`
      ml-1
      text-dslBlack-2
    `}
  }
`;

const DSRefreshButton = ({ onClick }) => {
  return (
    <StyledRefreshButton onClick={onClick} className="group">
      <Refresh />
      <div className="refresh-text">Refresh</div>
    </StyledRefreshButton>
  );
};

DSRefreshButton.propTypes = {
  onClick: PropTypes.func,
};

export default DSRefreshButton;
