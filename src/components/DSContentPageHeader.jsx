import PropTypes from 'prop-types';
import styled from 'styled-components';
import tw from 'twin.macro';
import { DateTime } from 'luxon';

import { DSRefreshButton } from '../components';
import useBreakPoint from '../hooks/useBreakPoint';

const StyledHeaderContainer = styled.div`
  ${tw`
    sticky top-0
    flex 
    flex-col
    md:flex-row
    justify-between
    px-5
    md:px-10
    py-7.5
    border-b border-solid border-dslGray-2
    bg-dslGray-1
    w-full
    z-20
  `};

  .ds-content-header-title-container {
    ${tw`
      flex flex-col
    `};

    .ds-content-header-title {
      ${tw`
        text-2xl font-medium leading-normal
        text-dslBlack-2
        mt-1.5
      `};
    }

    .ds-content-header-updated-date {
      ${tw`
        text-sm font-normal leading-6
        text-dslGray-4
        mr-3
      `};
    }

    .ds-content-header-sub-section {
      ${tw`flex items-center`}
    }
  }

  .ds-content-header-divider {
    ${tw`
      w-64 h-[1px]
      bg-dslBlack-2
      opacity-25
    `};
  }

  .ds-content-header-button-container {
    ${tw`
      flex flex-row
      gap-6
      items-start
      mt-4
      md:mt-0
    `};
  }
`;

const DSContentPageHeader = ({ buttons, title, onRefresh, ...props }) => {
  const lastUpdated = DateTime.now().toLocaleString({
    weekday: 'short',
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: 'numeric',
    minute: '2-digit',
  });

  const lastUpdatedSubtitle = `Last updated: ${lastUpdated}`;
  const breakpoint = useBreakPoint();
  const isMobile = breakpoint === 'xs' || breakpoint === 'sm';

  return (
    <StyledHeaderContainer {...props}>
      <div className="ds-content-header-title-container">
        <div className="ds-content-header-divider"></div>
        <div className="ds-content-header-title">{title}</div>
        <div className="ds-content-header-sub-section">
          <div className="ds-content-header-updated-date">
            {lastUpdatedSubtitle}
          </div>
          {!isMobile && <DSRefreshButton onClick={onRefresh} />}
        </div>
      </div>
      <div className="ds-content-header-button-container">{buttons}</div>
    </StyledHeaderContainer>
  );
};

DSContentPageHeader.propTypes = {
  buttons: PropTypes.arrayOf(PropTypes.node),
  title: PropTypes.string,
  onRefresh: PropTypes.func,
};

export default DSContentPageHeader;
