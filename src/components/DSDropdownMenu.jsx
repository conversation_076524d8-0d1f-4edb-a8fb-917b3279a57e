import { useState, useRef } from 'react';
import PropTypes from 'prop-types';
import styled from 'styled-components';
import tw from 'twin.macro';

import { DSButton } from './';
import { ChevronDown } from './svg/icons';

import { useOutsideClicked } from '../hooks/index.mjs';

const StyledDSMenuDropdown = styled.div`
  ${tw`
    relative
  `}
`;

const StyledDropdownItems = styled.div`
  ${tw`
    absolute
    right-0
    top-14
    w-full
    rounded-[3px]
    shadow-dslMedium
    bg-dslWhite
    border
    border-solid
    border-dslGray-2
    px-1.5
    md:px-1
    overflow-y-auto
    max-h-[266px]
    mt-[4px]
    z-20
  `}
`;

const StyledDropdownItem = styled.button`
  ${tw`
    flex
    items-center
    w-full
    p-[10px]
    mt-[4px]
    text-[14px]
    rounded-[3px]

    hover:(
      bg-dslGray-2
    )

    active:(
      bg-dslBlue-light
    )

    last:(
      mb-[4px]
    )
  `}

  .dropdown-item-text {
    ${tw`text-[14px]`}
  }
`;

const StyledDropdownIcon = styled.div`
  ${tw`
    flex
    items-center
    w-[24px]
    h-[24px]
  `}

  ${(props) => (props.isItem ? tw`mr-[10px]` : tw`mr-[5px]`)}

  svg {
    path {
      ${tw`fill-dslGray-4`}
    }
  }
`;

const DSDropdownMenu = ({ label, options, onSelectOption, variant }) => {
  const menuRef = useRef(null);
  const [open, setOpen] = useState(false);

  useOutsideClicked(menuRef, () => setOpen(false));

  const handleChangeSelection = (option) => {
    setOpen(false);
    onSelectOption(option);
  };

  return (
    <StyledDSMenuDropdown ref={menuRef}>
      <DSButton
        label={label}
        rightIcon={<ChevronDown />}
        variant={variant ?? 'primary'}
        onClick={() => setOpen(!open)}
        iconAutoWidth
        hug
      />
      {open && (
        <StyledDropdownItems>
          {options.map((option) => {
            const ItemIcon = option.icon;
            return (
              <StyledDropdownItem
                key={option?.id}
                onClick={() => handleChangeSelection(option)}
              >
                {ItemIcon && (
                  <StyledDropdownIcon isItem>
                    <ItemIcon />
                  </StyledDropdownIcon>
                )}
                <div className="dropdown-item-text">{option?.label}</div>
              </StyledDropdownItem>
            );
          })}
        </StyledDropdownItems>
      )}
    </StyledDSMenuDropdown>
  );
};

DSDropdownMenu.propTypes = {
  label: PropTypes.string,
  options: PropTypes.arrayOf(
    PropTypes.shape({
      id: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
      label: PropTypes.string,
      icon: PropTypes.elementType,
    }),
  ),
  onSelectOption: PropTypes.func,
  variant: PropTypes.string,
};

export default DSDropdownMenu;
