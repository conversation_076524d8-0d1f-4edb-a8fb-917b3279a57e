import PropTypes from 'prop-types';
import styled from 'styled-components';
import tw from 'twin.macro';

const StyledBannerContainer = styled.div`
  ${tw`
    flex items-center justify-center
    fixed top-0 left-0
    z-50
    text-sm
    w-full h-0
    overflow-hidden
    p-0
    transition-all duration-300 ease-in-out
    px-10
    text-center
  `};

  ${(props) => {
    switch (props.variant) {
      case 'info':
        return tw`bg-dslBlue-dark text-dslWhite`;
      case 'success':
        return tw`bg-dslStatus-green text-dslWhite`;
      case 'warning':
        return tw`bg-dslStatus-yellow text-dslBlack-1`;
      case 'error':
      default:
        return tw`bg-dslStatus-red text-dslWhite`;
    }
  }};

  ${(props) =>
    props.isVisible ? tw`h-auto px-7 py-3.5` : tw`h-0 p-0 duration-[0ms]`};
`;

const DSBanner = ({ body, isVisible, variant }) => {
  return (
    <StyledBannerContainer
      isVisible={isVisible}
      role="alert"
      id="password-error"
      variant={variant}
      aria-description={body}
    >
      {/* Only show body content if the banner is visible
      so that it can be read by screenreaders once 
      the banner is triggered: */}
      {isVisible ? body : ''}
    </StyledBannerContainer>
  );
};

DSBanner.propTypes = {
  body: PropTypes.string.isRequired,
  isVisible: PropTypes.bool,
  variant: PropTypes.oneOf(['error', 'info', 'success', 'warning']),
};

export default DSBanner;
