import styled from 'styled-components';
import tw, { theme } from 'twin.macro';
import PropTypes from 'prop-types';

import Alert from './svg/icons/small/Alert.jsx';

const StyledToastMessage = styled.div`
  ${tw`
    flex
    flex-row
    w-full
    md:w-[608px]
    border
    rounded-md
    py-3.5 px-2.5
    gap-2.5
    bg-dslGray-1
    shadow-dslMedium
  `};

  ${({ variant }) => {
    switch (variant) {
      case TOAST_VARIANTS.ERROR:
        return tw`border-dslStatus-red bg-dslStatus-redLight text-dslStatus-red`;
      case TOAST_VARIANTS.INFO:
      default:
        return tw`border-dslBlue-dark bg-dslGray-1 text-dslBlue-dark`;
    }
  }};

  .icon-wrapper {
    ${tw`
      flex
      items-center
    `};
  }

  .toast-content {
    ${tw`
      flex-1
    `};

    .title-text {
      ${tw`
        text-sm
        sm:text-base
        font-bold
        uppercase
      `};
    }

    .subtitle-text {
      ${tw`
        text-xs
        sm:text-sm
        font-normal
      `};
    }
  }

  .text-button {
    ${tw`
      text-xs
      sm:text-sm
      font-bold
      uppercase
      cursor-pointer
      flex
      flex-col
    `};

    .text-button-content {
      ${tw`
        flex
        flex-1
        items-center
      `};
    }
  }
`;

const DSToastMessage = ({ title, description, icon, onDismiss, variant }) => {
  let alertColor = theme`colors.dslBlue.dark`;
  if (variant === TOAST_VARIANTS.ERROR) {
    alertColor = theme`colors.dslStatus.red`;
  }
  return (
    <StyledToastMessage variant={variant}>
      <div className="icon-wrapper">
        {icon ? icon : <Alert color={alertColor} />}
      </div>
      <div className="toast-content">
        <div className="title-text">{title}</div>
        <div className="subtitle-text">{description}</div>
      </div>
      <div className="text-button" onClick={onDismiss}>
        <div className="text-button-content">dismiss</div>
      </div>
    </StyledToastMessage>
  );
};

DSToastMessage.propTypes = {
  title: PropTypes.string,
  description: PropTypes.oneOfType([PropTypes.string, PropTypes.element]),
  icon: PropTypes.element,
  variant: PropTypes.string,
  onDismiss: PropTypes.func.isRequired,
};

export const TOAST_VARIANTS = {
  INFO: 'info',
  ERROR: 'error',
};

export default DSToastMessage;
