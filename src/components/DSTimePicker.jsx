import { forwardRef, useState } from 'react';
import styled from 'styled-components';
import tw from 'twin.macro';
import DatePicker from 'react-datepicker';
import PropTypes from 'prop-types';

import { ChevronDown } from '../components/svg/icons';

const StyledDSTimePicker = styled.div`
  ${tw`
    w-full
  `};

  .react-datepicker {
    ${tw`
      font-sans
      px-6 py-5
      w-full
    `};
  }

  .react-datepicker-wrapper,
  .react-datepicker-popper {
    ${tw`w-full`}
  }

  .react-datepicker__triangle {
    ${tw`
      hidden
    `};
  }

  .react-datepicker__tab-loop {
    ${tw`
      relative
    `};
  }

  .react-datepicker--time-only {
    ${tw`p-0 text-start`}
  }

  .react-datepicker__tab-loop,
  .react-datepicker__time-container,
  .react-datepicker__time,
  .react-datepicker__time-box,
  .react-datepicker__time-list,
  .react-datepicker__time-list-item {
    ${tw`w-full!`}
  }

  .react-datepicker__header {
    ${tw`hidden`}
  }

  .react-datepicker__time-box {
    ${tw`p-0`}
  }

  .react-datepicker__time-list-item {
    ${tw`
      flex
      items-center
      justify-center
      h-10!
      mt-[4px]!
      text-[14px]
      rounded-[3px]

      hover:(
        bg-dslGray-2!
      )

      active:(
        bg-dslBlue-light!
      )

      last:(
        mb-[4px]
      )
    `}
  }

  .react-datepicker__time-list-item--selected {
    ${tw`
      bg-dslBlue-dark!
      hover:bg-dslBlue-dark!
    `}
  }
`;

const StyledTimePickerButton = styled.button`
  ${tw`
    flex
    items-center
    justify-between
    px-[15px]
    py-[14px]
    border
    border-solid
    border-dslGray-1
    rounded-[3px]
    w-full
    bg-dslWhite
    transition-all
    duration-300
    ease-in-out
    hover:(
      border-dslBlue-normal
    )
    disabled:(
      border-dslGray-2 
      bg-dslGray-2
      pointer-events-none
    )
  `}

  ${(props) => (props.open ? tw`border-dslBlue-normal` : tw``)}

  .dropdown-btn-left {
    ${tw`
      flex
      items-center
    `}

    .dropdown-btn-selected-text {
      ${tw`
        text-[14px]
        text-dslBlack-2
        outline-0
        transition
        ease-in-out
        duration-300
        disabled:(
          bg-dslGray-2
          text-dslGray-4
        )
      `}
    }
  }

  svg: &last-child {
    ${tw`
      transition
      ease-in-out
      duration-300
    `}
    ${(props) => (props.open ? tw`scale-y-[-1]` : tw``)}
  }
`;

const TimePickerButton = forwardRef(
  (
    { value, onClick, disabled, placeholder, onChange, onBlur, textEdit },
    ref,
  ) => {
    return (
      <StyledTimePickerButton ref={ref} onClick={onClick} disabled={disabled}>
        <div className="dropdown-btn-left">
          {textEdit ? (
            <input
              ref={ref}
              className="dropdown-btn-selected-text"
              value={value ? value : ''}
              placeholder={placeholder}
              onChange={onChange}
              onBlur={onBlur}
              disabled={disabled}
              type="text"
            />
          ) : (
            <div className="dropdown-btn-selected-text">
              {value ? value : placeholder}
            </div>
          )}
        </div>
        <ChevronDown color={disabled ? '#A6A6A6' : '#6B6B6B'} />
      </StyledTimePickerButton>
    );
  },
);

TimePickerButton.displayName = 'TimePickerButton';
TimePickerButton.propTypes = {
  value: PropTypes.string,
  onClick: PropTypes.func,
  onChange: PropTypes.func,
  onBlur: PropTypes.func,
  disabled: PropTypes.bool,
  placeholder: PropTypes.string,
  textEdit: PropTypes.bool,
};

// TODO: Separate functions for on blur and on change
// on blur saves the value and updates the end time
// on change captures the string value and saves it in anoteher variable
const DSTimePicker = ({
  placeholder,
  selectedTime,
  minDate,
  minTime,
  maxTime,
  onChange,
  onBlur,
  disabled,
  textEdit,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  return (
    <StyledDSTimePicker open={isOpen} disabled={disabled}>
      <DatePicker
        customInput={
          <TimePickerButton open={isOpen} onBlur={onBlur} textEdit={textEdit} />
        }
        selected={selectedTime}
        onChangeRaw={onChange}
        onChange={(date, event) => onBlur(date, event)}
        onBlur={onBlur}
        onCalendarOpen={() => setIsOpen(true)}
        onCalendarClose={() => setIsOpen(false)}
        showTimeSelect
        showTimeSelectOnly
        timeIntervals={15}
        dateFormat="h:mm aa"
        disabled={disabled}
        placeholderText={placeholder}
        minDate={minDate}
        minTime={minTime}
        maxTime={maxTime}
      />
    </StyledDSTimePicker>
  );
};

DSTimePicker.propTypes = {
  placeholder: PropTypes.string,
  selectedTime: PropTypes.instanceOf(Date),
  minDate: PropTypes.instanceOf(Date),
  minTime: PropTypes.instanceOf(Date),
  maxTime: PropTypes.instanceOf(Date),
  onChange: PropTypes.func,
  onBlur: PropTypes.func,
  onChangeInput: PropTypes.func,
  disabled: PropTypes.bool,
  textEdit: PropTypes.bool,
};

export default DSTimePicker;
