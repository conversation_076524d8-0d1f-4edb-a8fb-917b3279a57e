import PropTypes from 'prop-types';
import styled from 'styled-components';
import tw, { theme } from 'twin.macro';

export const BADGE_COLORS = {
  PRIMARY: 'primary',
  GREEN: 'green',
  BROWN: 'brown',
  OUTLINE: 'outline',
  OUTLINE_DARK: 'outlineDark',
  GRAY: 'gray',
  DARK_GRAY: 'darkGray',
  BLUE: 'blue',
  LIGHT_BLUE: 'lightBlue',
  BLACK: 'black',
  GRADIENT: 'gradient',
  YELLOW: 'yellow',
};

const BADGE_COLOR_ICON_MAP = {
  [BADGE_COLORS.PRIMARY]: theme`colors.dslWhite`,
  [BADGE_COLORS.GREEN]: theme`colors.dslWhite`,
  [BADGE_COLORS.BROWN]: theme`colors.dslWhite`,
  [BADGE_COLORS.OUTLINE]: theme`colors.dslWhite`,
  [BADGE_COLORS.OUTLINE_DARK]: theme`colors.dslBlue.dark`,
  [BADGE_COLORS.GRAY]: theme`colors.dslBlack.2`,
  [BADGE_COLORS.DARK_GRAY]: theme`colors.dslBlue.dark`,
  [BADGE_COLORS.BLUE]: theme`colors.dslWhite`,
  [BADGE_COLORS.LIGHT_BLUE]: theme`colors.dslBlue.dark`,
  [BADGE_COLORS.GRADIENT]: theme`colors.dslWhite`,
  [BADGE_COLORS.YELLOW]: theme`colors.dslStatus.yellowLight`,
};

const StyledDSBadge = styled.div`
  ${tw`
    inline-flex flex-row
    text-xs
    xs:max-sm:text-[11px]
    text-dslWhite
    font-medium
    px-2.5 py-1
    bg-dslBlue-dark
    rounded
    gap-1.5
    border border-transparent
    transition-all duration-300 ease-in-out
  `};

  ${({ noPointerEvents }) => (noPointerEvents ? tw`pointer-events-none` : tw``)}
  ${({ condensed }) => (condensed ? tw`px-1 py-[3px] gap-1` : undefined)};
  ${({ color }) => {
    switch (color) {
      case BADGE_COLORS.PRIMARY:
        return tw`bg-dslBlue-dark`;
      case BADGE_COLORS.GREEN:
        return tw`bg-dslGreen`;
      case BADGE_COLORS.BROWN:
        return tw`bg-dslBrown`;
      case BADGE_COLORS.OUTLINE:
        return tw`bg-transparent border-dslWhite`;
      case BADGE_COLORS.OUTLINE_DARK:
        return tw`bg-transparent border-dslBlue-dark text-dslBlue-dark`;
      case BADGE_COLORS.GRAY:
        return tw`bg-dslBlack-faded-5 text-dslBlack-2`;
      case BADGE_COLORS.DARK_GRAY:
        return tw`bg-dslGray-2 text-dslBlue-dark`;
      case BADGE_COLORS.BLUE:
        return tw`bg-dslBlue-normal text-dslBlack-2`;
      case BADGE_COLORS.LIGHT_BLUE:
        return tw`bg-dslBlue-light text-dslBlue-dark`;
      case BADGE_COLORS.BLACK:
        return tw`bg-dslBlack-1 text-dslWhite`;
      case BADGE_COLORS.GRADIENT:
        return tw`text-dslWhite bg-gradient-to-r from-cyan-500 to-indigo-700  whitespace-nowrap rounded-md border-0`;
      case BADGE_COLORS.YELLOW:
        return tw`bg-dslStatus-yellow text-dslStatus-yellowLight`;

      default:
        return undefined;
    }
  }};

  svg {
    ${tw`
      w-4 h-4
    `};
  }
`;

const DSBadge = ({
  condensed = false,
  iconElement,
  color,
  noPointerEvents,
  ...props
}) => {
  const Icon = iconElement;
  const iconColor = BADGE_COLOR_ICON_MAP[color];
  return (
    <StyledDSBadge
      condensed={condensed}
      color={color}
      noPointerEvents={noPointerEvents}
    >
      {Icon ? (
        <span>
          <Icon color={iconColor} />
        </span>
      ) : null}
      {props.children}
    </StyledDSBadge>
  );
};

DSBadge.propTypes = {
  condensed: PropTypes.bool,
  iconElement: PropTypes.func,
  color: PropTypes.oneOf(Object.values(BADGE_COLORS)),
  children: PropTypes.node,
  noPointerEvents: PropTypes.bool,
};

export default DSBadge;
