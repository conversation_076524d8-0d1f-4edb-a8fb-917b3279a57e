const LogoControlCenterDark = () => {
  return (
    <svg
      width="152"
      height="18"
      viewBox="0 0 152 18"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clipPath="url(#clip0_12896_2446)">
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M8.88726 0.306641C9.50928 1.9876 10.8346 3.31293 12.5156 3.93495C10.8346 4.55696 9.50928 5.88229 8.88726 7.56325C8.26525 5.88229 6.93992 4.55696 5.25896 3.93495C6.93992 3.31293 8.26525 1.9876 8.88726 0.306641ZM0.593994 9.11824C0.593994 13.6985 4.30702 17.4115 8.88726 17.4115C13.4675 17.4115 17.1805 13.6985 17.1805 9.11824H15.1072C15.1072 12.5534 12.3224 15.3382 8.88726 15.3382C5.45208 15.3382 2.66731 12.5534 2.66731 9.11824H0.593994ZM4.74063 9.11822C4.74063 8.30037 4.9774 7.53782 5.38614 6.89538C5.92208 7.39576 6.43259 7.92292 6.91569 8.47489C6.84965 8.67742 6.81395 8.89365 6.81395 9.11822C6.81395 10.2633 7.7422 11.1915 8.88726 11.1915C10.0323 11.1915 10.9606 10.2633 10.9606 9.11822C10.9606 8.81225 10.8943 8.52176 10.7753 8.26033C11.2345 7.70874 11.7319 7.19077 12.2635 6.71021C12.7485 7.38905 13.0339 8.22031 13.0339 9.11822C13.0339 11.4084 11.1773 13.2648 8.88726 13.2648C6.59714 13.2648 4.74063 11.4084 4.74063 9.11822Z"
          fill="url(#paint0_radial_12896_2446)"
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M31.7768 4.80415V6.88161C31.2668 6.62037 30.4706 6.2845 29.4008 6.2845C27.709 6.2845 26.7262 7.47873 26.7262 8.85956C26.7262 10.2528 27.6841 11.4346 29.3013 11.4346C30.4457 11.4346 31.3041 11.1112 31.839 10.8375V12.9398C31.2916 13.2259 30.2716 13.5121 29.1022 13.5121C26.4152 13.5121 24.4373 11.4719 24.4373 8.85956C24.4373 6.27206 26.4152 4.20703 29.2017 4.20703C30.396 4.20703 31.2294 4.51803 31.7768 4.80415ZM34.1111 8.85956C34.1111 6.29694 36.0891 4.20703 38.8508 4.20703C41.6124 4.20703 43.5904 6.2845 43.5904 8.85956C43.5904 11.4595 41.6248 13.5121 38.8508 13.5121C36.0891 13.5121 34.1111 11.4595 34.1111 8.85956ZM36.4001 8.85956C36.4001 10.2528 37.3953 11.4346 38.8508 11.4346C40.3062 11.4346 41.289 10.2528 41.289 8.85956C41.289 7.47873 40.3062 6.2845 38.8508 6.2845C37.3953 6.2845 36.4001 7.47873 36.4001 8.85956ZM53.8994 13.4623H53.9865V4.38119H51.7473V8.89688C51.6044 8.7897 51.4656 8.6702 51.3262 8.55019C51.223 8.46127 51.1192 8.37202 51.0134 8.28732L46.4603 4.25679H46.3608V13.3379H48.5876V8.82224L49.3091 9.41935L53.8994 13.4623ZM63.5693 6.29694H61.1808V13.3379H58.8919V6.29694H56.5034V4.38119H63.5693V6.29694ZM69.3571 4.38119H66.0979V13.3379H68.3246V10.3896H69.3198L70.7628 13.3379H73.1389L71.3351 10.0165C72.3178 9.58107 72.9771 8.72272 72.9771 7.42897C72.9771 5.4137 71.4968 4.38119 69.3571 4.38119ZM68.3246 6.29694H69.4566C70.1408 6.29694 70.6758 6.64525 70.6758 7.42897C70.6758 8.25 70.1533 8.57344 69.4691 8.57344H68.3246V6.29694ZM75.3345 8.85956C75.3345 6.29694 77.3125 4.20703 80.0741 4.20703C82.8358 4.20703 84.8137 6.2845 84.8137 8.85956C84.8137 11.4595 82.8482 13.5121 80.0741 13.5121C77.3125 13.5121 75.3345 11.4595 75.3345 8.85956ZM77.6235 8.85956C77.6235 10.2528 78.6186 11.4346 80.0741 11.4346C81.5296 11.4346 82.5123 10.2528 82.5123 8.85956C82.5123 7.47873 81.5296 6.2845 80.0741 6.2845C78.6186 6.2845 77.6235 7.47873 77.6235 8.85956ZM87.5842 13.3379H93.0951V11.4222H89.8607V4.38119H87.5842V13.3379ZM103.48 6.01073V4.8787C103.182 4.70454 102.174 4.23183 100.818 4.23183C98.1064 4.23183 96.1036 6.19733 96.1036 8.85947C96.1036 11.5216 98.1064 13.4871 100.818 13.4871C102.236 13.4871 103.244 13.0268 103.543 12.8526V11.7206C102.958 12.0192 102.025 12.3799 100.818 12.3799C98.7781 12.3799 97.3102 10.8871 97.3102 8.85947C97.3102 6.83177 98.7781 5.33898 100.818 5.33898C101.963 5.33898 102.896 5.71218 103.48 6.01073ZM106.632 13.3378H112.317V12.3302H107.826V9.30731H111.458V8.29968H107.826V5.38874H112.13V4.38111H106.632V13.3378ZM122.582 13.4871H122.569L117.009 7.45376L116.723 7.14277V13.3378H115.528V4.23183H115.541L121.101 10.2651L121.388 10.5761V4.38111H122.582V13.4871ZM129.34 5.38874H132.077V4.38111H125.421V5.38874H128.146V13.3378H129.34V5.38874ZM140.598 13.3378H134.913V4.38111H140.411V5.38874H136.107V8.29968H139.74V9.30731H136.107V12.3302H140.598V13.3378ZM146.708 4.38111H143.809V13.3378H145.004V9.82979H146.87L148.723 13.3378H150.054L148.027 9.63075C148.984 9.33219 149.855 8.4614 149.855 7.11789C149.855 5.40118 148.673 4.38111 146.708 4.38111ZM145.004 5.38874H146.671C147.915 5.38874 148.661 6.03561 148.661 7.11789C148.661 8.20016 147.952 8.84703 146.77 8.84703H145.004V5.38874Z"
          fill="#1B1B1B"
        />
      </g>
      <defs>
        <radialGradient
          id="paint0_radial_12896_2446"
          cx="0"
          cy="0"
          r="1"
          gradientUnits="userSpaceOnUse"
          gradientTransform="translate(14.7324 4.36521) rotate(138.315) scale(13.4183 13.2526)"
        >
          <stop stopColor="#2E3191" />
          <stop offset="1" stopColor="#00B8F1" />
        </radialGradient>
        <clipPath id="clip0_12896_2446">
          <rect
            width="149.46"
            height="17.1049"
            fill="white"
            transform="translate(0.593994 0.306641)"
          />
        </clipPath>
      </defs>
    </svg>
  );
};

export default LogoControlCenterDark;
