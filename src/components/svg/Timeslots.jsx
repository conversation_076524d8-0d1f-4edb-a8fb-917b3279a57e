const Timeslots = () => {
  return (
    <svg
      width="46"
      height="46"
      viewBox="0 0 46 46"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clipPath="url(#clip0_12467_4355)">
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M13.7292 2.49294C16.5576 1.21238 19.6971 0.5 23 0.5C26.3029 0.5 29.4424 1.21238 32.2708 2.49294L33.2872 2.95311L32.3668 4.98588L31.3504 4.52571C28.8055 3.37347 25.9792 2.73141 23 2.73141C20.0208 2.73141 17.1945 3.37347 14.6496 4.52571L13.6332 4.98588L12.7129 2.95311L13.7292 2.49294ZM11.8442 6.03819L10.9477 6.70232C10.4862 7.04424 10.0396 7.40531 9.60928 7.78437L8.77203 8.52181L7.29716 6.84732L8.13441 6.10988C8.61186 5.68935 9.10732 5.28872 9.61948 4.90931L10.516 4.24518L11.8442 6.03819ZM39.1527 7.29716L39.8901 8.13441C40.3107 8.61186 40.7113 9.10732 41.0907 9.61948L41.7548 10.516L39.9618 11.8442L39.2977 10.9477C38.9558 10.4862 38.5947 10.0396 38.2156 9.60928L37.4782 8.77203L39.1527 7.29716ZM4.98588 13.6332L4.52571 14.6496C3.37347 17.1945 2.73141 20.0208 2.73141 23C2.73141 25.9792 3.37347 28.8055 4.52571 31.3504L4.98588 32.3668L2.95311 33.2872L2.49294 32.2708C1.21238 29.4424 0.5 26.3029 0.5 23C0.5 19.6971 1.21238 16.5576 2.49294 13.7292L2.95311 12.7129L4.98588 13.6332ZM43.0469 12.7129L43.5071 13.7292C44.7876 16.5576 45.5 19.6971 45.5 23C45.5 26.3029 44.7876 29.4424 43.5071 32.2708L43.0469 33.2872L41.0141 32.3668L41.4743 31.3504C42.6265 28.8055 43.2686 25.9792 43.2686 23C43.2686 20.0208 42.6265 17.1945 41.4743 14.6496L41.0141 13.6332L43.0469 12.7129ZM6.03819 34.1558L6.70232 35.0523C7.04424 35.5138 7.40531 35.9604 7.78437 36.3907L8.52181 37.228L6.84732 38.7028L6.10988 37.8656C5.68934 37.3881 5.28872 36.8927 4.90931 36.3805L4.24518 35.484L6.03819 34.1558ZM38.7028 39.1527L37.8656 39.8901C37.3881 40.3107 36.8927 40.7113 36.3805 41.0907L35.484 41.7548L34.1558 39.9618L35.0523 39.2977C35.5138 38.9558 35.9604 38.5947 36.3907 38.2156L37.228 37.4782L38.7028 39.1527ZM13.6332 41.0141L14.6496 41.4743C17.1945 42.6265 20.0208 43.2686 23 43.2686C25.9792 43.2686 28.8055 42.6265 31.3504 41.4743L32.3668 41.0141L33.2872 43.0469L32.2708 43.5071C29.4424 44.7876 26.3029 45.5 23 45.5C19.6971 45.5 16.5576 44.7876 13.7292 43.5071L12.7129 43.0469L13.6332 41.0141Z"
          fill="#A6A6A6"
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M18.5 13.5016V12.5H20.5V13.5H26.5V12.5H28.5V13.5016C28.9755 13.5049 29.391 13.5147 29.7518 13.5442C30.3139 13.5901 30.8306 13.6887 31.316 13.936C32.0686 14.3195 32.6805 14.9314 33.064 15.684C33.3113 16.1694 33.4099 16.6861 33.4558 17.2482C33.5 17.7894 33.5 18.4537 33.5 19.2587V27.7413C33.5 28.5463 33.5 29.2106 33.4558 29.7518C33.4099 30.3139 33.3113 30.8306 33.064 31.316C32.6805 32.0686 32.0686 32.6805 31.316 33.064C30.8306 33.3113 30.3139 33.4099 29.7518 33.4558C29.2106 33.5 28.5463 33.5 27.7413 33.5H19.2587C18.4537 33.5 17.7894 33.5 17.2482 33.4558C16.6861 33.4099 16.1694 33.3113 15.684 33.064C14.9314 32.6805 14.3195 32.0686 13.936 31.316C13.6887 30.8306 13.5901 30.3139 13.5442 29.7518C13.5 29.2106 13.5 28.5463 13.5 27.7413V19.2587C13.5 18.4537 13.5 17.7894 13.5442 17.2482C13.5901 16.6861 13.6887 16.1694 13.936 15.684C14.3195 14.9314 14.9314 14.3195 15.684 13.936C16.1694 13.6887 16.6861 13.5901 17.2482 13.5442C17.609 13.5147 18.0245 13.5049 18.5 13.5016ZM18.5 15.5018C18.0545 15.5049 17.7046 15.5136 17.411 15.5376C16.9726 15.5734 16.7484 15.6383 16.592 15.718C16.2157 15.9097 15.9097 16.2157 15.718 16.592C15.6383 16.7484 15.5734 16.9726 15.5376 17.411C15.5008 17.8611 15.5 18.4434 15.5 19.3V19.5H31.5V19.3C31.5 18.4434 31.4992 17.8611 31.4624 17.411C31.4266 16.9726 31.3617 16.7484 31.282 16.592C31.0903 16.2157 30.7843 15.9097 30.408 15.718C30.2516 15.6383 30.0274 15.5734 29.589 15.5376C29.2954 15.5136 28.9455 15.5049 28.5 15.5018V16.5H26.5V15.5H20.5V16.5H18.5V15.5018ZM31.5 21.5H15.5V27.7C15.5 28.5566 15.5008 29.1389 15.5376 29.589C15.5734 30.0274 15.6383 30.2516 15.718 30.408C15.9097 30.7843 16.2157 31.0903 16.592 31.282C16.7484 31.3617 16.9726 31.4266 17.411 31.4624C17.8611 31.4992 18.4434 31.5 19.3 31.5H27.7C28.5566 31.5 29.1389 31.4992 29.589 31.4624C30.0274 31.4266 30.2516 31.3617 30.408 31.282C30.7843 31.0903 31.0903 30.7843 31.282 30.408C31.3617 30.2516 31.4266 30.0274 31.4624 29.589C31.4992 29.1389 31.5 28.5566 31.5 27.7V21.5Z"
          fill="#6B6B6B"
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M22.2071 26.7929L25.5 23.5L26.9142 24.9142L22.2071 29.6213L19.5 26.9142L20.9142 25.5L22.2071 26.7929Z"
          fill="#6B6B6B"
        />
      </g>
      <defs>
        <clipPath id="clip0_12467_4355">
          <rect
            width="45"
            height="45"
            fill="white"
            transform="translate(0.5 0.5)"
          />
        </clipPath>
      </defs>
    </svg>
  );
};

export default Timeslots;
