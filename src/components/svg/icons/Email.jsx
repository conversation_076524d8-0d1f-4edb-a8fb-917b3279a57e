import PropTypes from 'prop-types';

const Email = ({ color = '#6B6B6B' }) => (
  <svg
    width={22}
    height={20}
    viewBox="0 0 22 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M15.293 1.707L17.586 4H10v2h7.586l-2.293 2.293 1.414 1.414L21.414 5 16.707.293l-1.414 1.414zM14 10.5l-1.235-1.235L10 10.99 2 6h5.5V4H2C.9 4 0 4.9 0 6v12c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2v-7.687l-2 1.875V18H2V8l8 5 4-2.5z"
      fill={color}
    />
  </svg>
);

Email.propTypes = {
  color: PropTypes.string,
};

export default Email;
