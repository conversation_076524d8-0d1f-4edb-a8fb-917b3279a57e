import PropTypes from 'prop-types';

const MenuIcon = ({ color = '#FFFFFF' }) => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M3 6V8H21V6H3ZM3 11V13H21V11H3ZM3 18V16H21V18H3Z"
      fill={color}
    />
  </svg>
);

MenuIcon.propTypes = {
  color: PropTypes.string,
};

export default MenuIcon;
