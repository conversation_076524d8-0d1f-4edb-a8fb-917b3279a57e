import PropTypes from 'prop-types';

const VerticalMenu = ({ width = 24, height = 24, color = '#DDDEFF' }) => (
  <svg
    width={width}
    height={height}
    viewBox="0 0 24 24"
    fill={color}
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M10 24H14V20H10V24ZM10 0V4H14V0H10ZM10 14H14V10H10V14Z"
      fill={color}
    />
  </svg>
);

VerticalMenu.propTypes = {
  width: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  height: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  color: PropTypes.string,
};

export default VerticalMenu;
