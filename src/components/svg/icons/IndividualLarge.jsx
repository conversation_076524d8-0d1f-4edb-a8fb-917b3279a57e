import PropTypes from 'prop-types';

const IndividualLarge = ({ color = '#1B1B1B' }) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="30"
      height="31"
      viewBox="0 0 30 31"
      fill="none"
    >
      <path
        d="M15 6.75C12.5828 6.75 10.625 8.70781 10.625 11.125C10.625 13.5422 12.5828 15.5 15 15.5C17.4172 15.5 19.375 13.5422 19.375 11.125C19.375 8.70781 17.4172 6.75 15 6.75ZM15 17.6875C12.0797 17.6875 6.25 19.1531 6.25 22.0625V24.25H23.75V22.0625C23.75 19.1531 17.9203 17.6875 15 17.6875Z"
        fill={color}
      />
    </svg>
  );
};

IndividualLarge.propTypes = {
  color: PropTypes.string,
};

export default IndividualLarge;
