import PropTypes from 'prop-types';

const SquareUnion = ({ width = '14', height = '14', color = '#6B6B6B' }) => (
  <svg
    width={width}
    height={height}
    viewBox="0 0 14 14"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M14 14H0V0H14V14ZM2 12H12V2H2V12ZM10 10H4V4H10V10ZM5 9H9V5H5V9Z"
      fill={color}
    />
  </svg>
);

SquareUnion.propTypes = {
  width: PropTypes.string,
  height: PropTypes.string,
  color: PropTypes.string,
};

export default SquareUnion;
