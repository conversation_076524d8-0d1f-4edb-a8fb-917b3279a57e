import PropTypes from 'prop-types';

const EyeOn = ({ color = '#1B1B1B' }) => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M6.75722 8.93351C5.45297 10.0221 4.53871 11.3029 4.07639 12.0349C4.05261 12.0726 4.03473 12.1009 4.01986 12.125C4.03473 12.1491 4.05261 12.1774 4.07639 12.2151C4.53871 12.9471 5.45297 14.2279 6.75722 15.3165C8.05913 16.4031 9.68937 17.25 11.6136 17.25C13.5379 17.25 15.1681 16.4031 16.47 15.3165C17.7743 14.2279 18.6886 12.9471 19.1509 12.2151C19.1747 12.1774 19.1925 12.1491 19.2074 12.125C19.1925 12.1009 19.1747 12.0726 19.1509 12.0349C18.6886 11.3029 17.7743 10.0221 16.47 8.93351C15.1681 7.84692 13.5379 7 11.6136 7C9.68936 7 8.05913 7.84692 6.75722 8.93351ZM5.47569 7.39803C7.00226 6.12394 9.07102 5 11.6136 5C14.1562 5 16.225 6.12394 17.7516 7.39803C19.2758 8.67016 20.3195 10.1398 20.8419 10.967C20.8489 10.9781 20.8562 10.9895 20.8637 11.0013C20.9663 11.1627 21.1107 11.3897 21.1832 11.7061C21.242 11.9624 21.242 12.2876 21.1832 12.5439C21.1107 12.8603 20.9663 13.0873 20.8637 13.2487C20.8562 13.2605 20.8489 13.2719 20.8419 13.283C20.3195 14.1102 19.2758 15.5798 17.7516 16.852C16.225 18.1261 14.1562 19.25 11.6136 19.25C9.07103 19.25 7.00226 18.1261 5.47569 16.852C3.95148 15.5798 2.9078 14.1102 2.38539 13.283C2.37838 13.2719 2.3711 13.2605 2.36361 13.2487C2.26094 13.0873 2.1166 12.8603 2.04407 12.5439C1.98531 12.2876 1.98531 11.9624 2.04407 11.7061C2.1166 11.3897 2.26094 11.1627 2.36361 11.0013C2.37111 10.9895 2.37838 10.9781 2.38539 10.967C2.9078 10.1398 3.95148 8.67016 5.47569 7.39803ZM11.6136 10.5C10.7162 10.5 9.98863 11.2275 9.98863 12.125C9.98863 13.0225 10.7162 13.75 11.6136 13.75C12.5111 13.75 13.2386 13.0225 13.2386 12.125C13.2386 11.2275 12.5111 10.5 11.6136 10.5ZM7.98863 12.125C7.98863 10.123 9.6116 8.5 11.6136 8.5C13.6157 8.5 15.2386 10.123 15.2386 12.125C15.2386 14.127 13.6157 15.75 11.6136 15.75C9.6116 15.75 7.98863 14.127 7.98863 12.125Z"
      fill={color}
    />
  </svg>
);

EyeOn.propTypes = {
  color: PropTypes.string,
};

export default EyeOn;
