import PropTypes from 'prop-types';

const SignOut = ({ color = '#1B1B1B' }) => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M9.51764 4.06812C9.29536 4.00856 8.99435 3.99997 8 3.99997H7.5C6.78741 3.99997 6.30304 4.00052 5.92661 4.0262C5.55915 4.05127 5.36813 4.09692 5.23463 4.15222C4.74458 4.3552 4.35523 4.74455 4.15224 5.23461C4.09694 5.36811 4.0513 5.55912 4.02623 5.92658C4.00054 6.30302 4 6.78738 4 7.49997V16.5C4 17.2126 4.00054 17.6969 4.02623 18.0734C4.0513 18.4408 4.09694 18.6318 4.15224 18.7653C4.35523 19.2554 4.74458 19.6447 5.23463 19.8477C5.36813 19.903 5.55915 19.9487 5.9266 19.9737C6.30304 19.9994 6.78741 20 7.5 20H8C8.99435 20 9.29536 19.9914 9.51764 19.9318C10.2078 19.7469 10.7469 19.2078 10.9319 18.5176C10.9914 18.2953 11 17.9943 11 17V16H13V17C13 17.0465 13 17.0924 13.0001 17.1375C13.0005 17.9329 13.0008 18.5236 12.8637 19.0353C12.4938 20.4156 11.4156 21.4938 10.0353 21.8637C9.52363 22.0008 8.93296 22.0005 8.13758 22C8.09239 22 8.04654 22 8 22H7.46573C6.79594 22 6.24307 22 5.79046 21.9691C5.32118 21.9371 4.88708 21.8686 4.46927 21.6955C3.48915 21.2895 2.71046 20.5108 2.30448 19.5307C2.13142 19.1129 2.06288 18.6788 2.03087 18.2095C1.99998 17.7569 1.99999 17.204 2 16.5342V7.46571C1.99999 6.79591 1.99998 6.24304 2.03087 5.79044C2.06288 5.32115 2.13142 4.88705 2.30448 4.46924C2.71046 3.48913 3.48916 2.71043 4.46927 2.30446C4.88708 2.13139 5.32118 2.06286 5.79046 2.03084C6.24307 1.99996 6.79594 1.99997 7.46573 1.99997L8 1.99997C8.04654 1.99997 8.09239 1.99995 8.13758 1.99993C8.93297 1.99949 9.52363 1.99917 10.0353 2.13627C11.4156 2.50614 12.4938 3.58433 12.8637 4.9647C13.0008 5.47635 13.0005 6.06701 13.0001 6.8624C13 6.90759 13 6.95344 13 6.99997V7.99997H11V6.99997C11 6.00562 10.9914 5.70461 10.9319 5.48234C10.7469 4.79215 10.2078 4.25306 9.51764 4.06812ZM16 5.58576L22.4142 12L16 18.4142L14.5858 17L18.5858 13H8V11H18.5858L14.5858 6.99997L16 5.58576Z"
      fill={color}
    />
  </svg>
);

SignOut.propTypes = {
  color: PropTypes.string,
};

export default SignOut;
