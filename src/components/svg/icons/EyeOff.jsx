import PropTypes from 'prop-types';

const EyeOff = ({ color = '#1B1B1B' }) => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M5.47397 7.14955L3.03154 4.70712L4.44575 3.29291L20.1958 19.0429L18.7815 20.4571L16.0899 17.7655C14.8277 18.4886 13.3273 19 11.6136 19C9.07104 19 7.00227 17.8761 5.4757 16.602C3.95149 15.3299 2.90781 13.8602 2.3854 13.033C2.37839 13.0219 2.37111 13.0105 2.36362 12.9987C2.26093 12.8373 2.11658 12.6103 2.04405 12.2938C1.9853 12.0375 1.98532 11.7123 2.0441 11.4559L3.01206 11.6779L2.0441 11.4559C2.11667 11.1394 2.26143 10.9119 2.36436 10.7501C2.37192 10.7383 2.37925 10.7267 2.38631 10.7156L3.23175 11.2496L2.38631 10.7156C2.90474 9.89489 3.94702 8.42451 5.47397 7.14955ZM6.89491 8.57049C5.52286 9.67742 4.56019 11.0191 4.07719 11.7837C4.05305 11.8219 4.03494 11.8506 4.01987 11.875C4.03474 11.8991 4.05262 11.9274 4.0764 11.9651C4.53872 12.6971 5.45298 13.978 6.75723 15.0665C8.05914 16.1531 9.68938 17 11.6136 17C12.7113 17 13.7119 16.725 14.6113 16.2869L13.3707 15.0462C12.8502 15.3351 12.2508 15.5 11.6136 15.5C9.61161 15.5 7.98864 13.877 7.98864 11.875C7.98864 11.2379 8.15356 10.6385 8.44241 10.118L6.89491 8.57049ZM10.0006 11.6762C9.99271 11.7413 9.98864 11.8077 9.98864 11.875C9.98864 12.7725 10.7162 13.5 11.6136 13.5C11.681 13.5 11.7473 13.4959 11.8125 13.488L10.0006 11.6762ZM11.6136 6.75001C11.2858 6.75001 10.9678 6.77447 10.6596 6.82004L10.3671 4.84155C10.7701 4.78196 11.1859 4.75001 11.6136 4.75001C14.1563 4.75001 16.225 5.87396 17.7516 7.14805C19.2758 8.42018 20.3195 9.88982 20.8419 10.717L20.2334 11.1013L20.8419 10.717C20.8489 10.7281 20.8562 10.7395 20.8637 10.7513C20.9664 10.9128 21.1107 11.1397 21.1833 11.4562C21.242 11.7126 21.242 12.0378 21.1832 12.2942L20.2085 12.0706L21.1832 12.2942C21.1106 12.6107 20.9656 12.8386 20.8625 13.0006C20.8549 13.0125 20.8476 13.024 20.8405 13.0353C20.5596 13.4798 20.1337 14.101 19.5694 14.7749L18.036 13.4909C18.5316 12.899 18.9058 12.353 19.1497 11.967C19.174 11.9285 19.1922 11.8996 19.2074 11.875C19.1925 11.851 19.1747 11.8226 19.1509 11.785L19.9964 11.251L19.1509 11.785C18.6886 11.0529 17.7743 9.77207 16.4701 8.68352C15.1682 7.59694 13.5379 6.75001 11.6136 6.75001Z"
      fill={color}
    />
  </svg>
);

EyeOff.propTypes = {
  color: PropTypes.string,
};

export default EyeOff;
