import PropTypes from 'prop-types';

const Individual = ({ color = '#6B6B6B' }) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
    >
      <path
        d="M12 5C10.3425 5 9 6.3425 9 8C9 9.6575 10.3425 11 12 11C13.6575 11 15 9.6575 15 8C15 6.3425 13.6575 5 12 5ZM12 12.5C9.9975 12.5 6 13.505 6 15.5V17H18V15.5C18 13.505 14.0025 12.5 12 12.5Z"
        fill={color}
      />
    </svg>
  );
};

Individual.propTypes = {
  color: PropTypes.string,
};

export default Individual;
