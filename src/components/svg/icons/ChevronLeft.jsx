import PropTypes from 'prop-types';
import styled from 'styled-components';
import tw from 'twin.macro';

export const ICON_DIRECTION = {
  LEFT: 'left',
  RIGHT: 'right',
  UP: 'up',
  DOWN: 'down',
};

const StyledSVG = styled.svg`
  ${({ rotate }) => {
    switch (rotate) {
      case ICON_DIRECTION.LEFT:
        return tw`transform rotate-0`;
      case ICON_DIRECTION.RIGHT:
        return tw`transform rotate-180`;
      case ICON_DIRECTION.UP:
        return tw`transform rotate-90`;
      case ICON_DIRECTION.DOWN:
        return tw`transform rotate-[270deg]`;
      default:
        return tw`transform rotate-0`;
    }
  }};
`;

const ChevronLeft = ({ color = '#1B1B1B', rotate = ICON_DIRECTION.LEFT }) => (
  <StyledSVG
    rotate={rotate}
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M13.7928 5.79291L15.207 7.20712L10.9141 11.5L15.207 15.7929L13.7928 17.2071L8.08571 11.5L13.7928 5.79291Z"
      fill={color}
    />
  </StyledSVG>
);

ChevronLeft.propTypes = {
  color: PropTypes.string,
  rotate: PropTypes.oneOf(Object.values(ICON_DIRECTION)),
};

export default ChevronLeft;
