import PropTypes from 'prop-types';

const SiteGroupEnvironment = ({ color = '#00B8F1' }) => {
  return (
    <svg
      width="14"
      height="14"
      viewBox="0 0 14 14"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M0 3C0 1.34315 1.34315 0 3 0H11C12.6569 0 14 1.34315 14 3V11C14 12.6569 12.6569 14 11 14H3C1.34315 14 0 12.6569 0 11V3Z"
        fill={color}
      />
      <path
        d="M7 3L7.29477 3.7966C7.7934 5.14414 8.85586 6.2066 10.2034 6.70523L11 7L10.2034 7.29477C8.85586 7.7934 7.7934 8.85586 7.29477 10.2034L7 11L6.70523 10.2034C6.2066 8.85586 5.14414 7.7934 3.7966 7.29477L3 7L3.7966 6.70523C5.14414 6.2066 6.2066 5.14414 6.70523 3.7966L7 3Z"
        fill="black"
      />
    </svg>
  );
};

SiteGroupEnvironment.propTypes = {
  color: PropTypes.string,
};

export default SiteGroupEnvironment;
