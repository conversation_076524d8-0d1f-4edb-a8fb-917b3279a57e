import PropTypes from 'prop-types';

const Group = ({ color = '#1B1B1B' }) => (
  <svg
    width="14"
    height="14"
    viewBox="0 0 14 14"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M4.9 7.4375C6.251 7.4375 7.35 6.35812 7.35 5.03125C7.35 3.70438 6.251 2.625 4.9 2.625C3.549 2.625 2.45 3.70438 2.45 5.03125C2.45 6.35812 3.549 7.4375 4.9 7.4375ZM4.9 4C5.481 4 5.95 4.46063 5.95 5.03125C5.95 5.60188 5.481 6.0625 4.9 6.0625C4.319 6.0625 3.85 5.60188 3.85 5.03125C3.85 4.46063 4.319 4 4.9 4ZM4.935 10.875H1.939C2.632 10.5312 3.829 10.1875 4.9 10.1875C4.977 10.1875 5.061 10.1944 5.138 10.1944C5.376 9.6925 5.789 9.28 6.286 8.95C5.775 8.86062 5.292 8.8125 4.9 8.8125C3.262 8.8125 0 9.61688 0 11.2188V12.25H4.9V11.2188C4.9 11.1019 4.914 10.985 4.935 10.875ZM10.15 9.15625C8.862 9.15625 6.3 9.85063 6.3 11.2188V12.25H14V11.2188C14 9.85063 11.438 9.15625 10.15 9.15625ZM10.997 7.905C11.529 7.60938 11.9 7.0525 11.9 6.40625C11.9 5.4575 11.116 4.6875 10.15 4.6875C9.184 4.6875 8.4 5.4575 8.4 6.40625C8.4 7.0525 8.771 7.60938 9.303 7.905C9.555 8.0425 9.842 8.125 10.15 8.125C10.458 8.125 10.745 8.0425 10.997 7.905Z"
      fill={color}
    />
  </svg>
);

Group.propTypes = {
  color: PropTypes.string,
};

export default Group;
