import PropTypes from 'prop-types';

const Alert = ({ color = '#2E3191' }) => {
  return (
    <svg
      width={14}
      height={15}
      viewBox="0 0 14 15"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M6 9.5h2v-7H6v7zm0 3h2v-2H6v2zm-3-9h1v2H3v4h1v2H3a2 2 0 01-2-2v-4a2 2 0 012-2zm8 6h-1v2h1a2 2 0 002-2v-4a2 2 0 00-2-2h-1v2h1v4z"
        fill={color}
      />
    </svg>
  );
};

Alert.propTypes = {
  color: PropTypes.string,
};

export default Alert;
