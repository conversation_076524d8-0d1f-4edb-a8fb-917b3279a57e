import PropTypes from 'prop-types';

const Home = ({ color = '#1B1B1B' }) => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M11.4773 1.33636C11.8196 1.24368 12.1804 1.24368 12.5227 1.33636C12.9201 1.44395 13.2546 1.7066 13.5216 1.91624C13.5471 1.93625 13.572 1.95578 13.5963 1.97465L20.3786 7.24977C20.4047 7.27008 20.4306 7.29019 20.4563 7.31013C20.8329 7.60241 21.1647 7.85991 21.412 8.19421C21.629 8.48759 21.7906 8.8181 21.889 9.1695C22.0011 9.56992 22.0006 9.98993 22.0001 10.4667C22.0001 10.4992 22 10.532 22 10.5651V17.8386C22 18.3657 22.0001 18.8204 21.9694 19.195C21.9371 19.5904 21.8658 19.9836 21.673 20.362C21.3854 20.9264 20.9265 21.3854 20.362 21.673C19.9836 21.8658 19.5904 21.9371 19.195 21.9694C18.8205 22 18.3657 22 17.8386 22H6.16145C5.63431 22 5.17956 22 4.80499 21.9694C4.40964 21.9371 4.01643 21.8658 3.63805 21.673C3.07356 21.3854 2.61462 20.9264 2.327 20.362C2.1342 19.9836 2.06289 19.5904 2.03059 19.195C1.99998 18.8204 2 18.3657 2.00002 17.8385L2.00002 10.5651C2.00002 10.532 1.99998 10.4992 1.99995 10.4667C1.99943 9.98993 1.99898 9.56992 2.11106 9.1695C2.20942 8.8181 2.37107 8.48759 2.58806 8.19421C2.83532 7.85991 3.16713 7.6024 3.54377 7.31012C3.56946 7.29018 3.59537 7.27007 3.62147 7.24977L10.4038 1.97465C10.428 1.95578 10.4529 1.93625 10.4784 1.91623C10.7454 1.7066 11.08 1.44395 11.4773 1.33636ZM11.9927 3.28153C11.9176 3.33174 11.8186 3.40798 11.6317 3.55335L4.84935 8.82848C4.35127 9.21587 4.25852 9.29901 4.19603 9.3835C4.1237 9.4813 4.06982 9.59147 4.03703 9.7086C4.0087 9.8098 4.00002 9.93406 4.00002 10.5651V17.8C4.00002 18.3765 4.0008 18.7488 4.02394 19.0321C4.04614 19.3038 4.08382 19.4045 4.10901 19.454C4.20488 19.6421 4.35787 19.7951 4.54603 19.891C4.59547 19.9162 4.69619 19.9539 4.96785 19.9761C5.25119 19.9992 5.62346 20 6.20002 20H17.8C18.3766 20 18.7488 19.9992 19.0322 19.9761C19.3038 19.9539 19.4046 19.9162 19.454 19.891C19.6422 19.7951 19.7951 19.6421 19.891 19.454C19.9162 19.4045 19.9539 19.3038 19.9761 19.0321C19.9992 18.7488 20 18.3765 20 17.8V10.5651C20 9.93406 19.9913 9.8098 19.963 9.7086C19.9302 9.59146 19.8763 9.4813 19.804 9.3835C19.7415 9.29901 19.6488 9.21587 19.1507 8.82848L12.3684 3.55335C12.1815 3.40798 12.0825 3.33174 12.0073 3.28153C12.0048 3.27983 12.0023 3.27822 12 3.27669C11.9977 3.27822 11.9953 3.27983 11.9927 3.28153ZM12 11C10.8954 11 10 11.8954 10 13C10 14.1046 10.8954 15 12 15C13.1046 15 14 14.1046 14 13C14 11.8954 13.1046 11 12 11ZM8.00002 13C8.00002 10.7908 9.79088 8.99998 12 8.99998C14.2092 8.99998 16 10.7908 16 13C16 15.2091 14.2092 17 12 17C9.79088 17 8.00002 15.2091 8.00002 13Z"
      fill={color}
    />
  </svg>
);

Home.propTypes = {
  color: PropTypes.string,
};

export default Home;
