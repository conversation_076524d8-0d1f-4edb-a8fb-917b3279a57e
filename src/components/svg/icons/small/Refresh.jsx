import PropTypes from 'prop-types';

const Refresh = ({ color = '#6B6B6B' }) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="14"
      height="15"
      viewBox="0 0 14 15"
      fill="none"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M9.58528 5.29096L9.41254 5.11822C8.06583 3.77151 5.87739 3.77102 4.53184 5.11656C3.1863 6.4621 3.18679 8.65055 4.5335 9.99725C5.21346 10.6772 6.10618 11.0136 6.99751 11.0065L7.29989 11.0041V12.8516L7.00221 12.8539C5.63748 12.8645 4.2697 12.3497 3.22535 11.3054C1.15337 9.23342 1.15725 5.8826 3.22757 3.81228C5.29788 1.74196 8.6487 1.73809 10.7207 3.81007L10.8938 3.98322L10.9081 3.96899L12.3777 2.50024V6.87524H8L9.58528 5.29096Z"
        fill={color}
      />
    </svg>
  );
};

Refresh.propTypes = {
  color: PropTypes.string,
};

export default Refresh;
