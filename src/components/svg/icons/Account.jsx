import PropTypes from 'prop-types';

const Account = ({ color = '#1B1B1B' }) => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M7 7C7 4.23772 9.23772 2 12 2C14.7623 2 17 4.23772 17 7C17 9.76228 14.7623 12 12 12C9.23772 12 7 9.76228 7 7ZM12 4C10.3423 4 9 5.34228 9 7C9 8.65772 10.3423 10 12 10C13.6577 10 15 8.65772 15 7C15 5.34228 13.6577 4 12 4Z"
      fill={color}
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M7 16C5.89543 16 5 16.8954 5 18V21H3V18C3 15.7909 4.79086 14 7 14H17C19.2091 14 21 15.7909 21 18V21H19V18C19 16.8954 18.1046 16 17 16H7Z"
      fill={color}
    />
  </svg>
);

Account.propTypes = {
  color: PropTypes.string,
};

export default Account;
