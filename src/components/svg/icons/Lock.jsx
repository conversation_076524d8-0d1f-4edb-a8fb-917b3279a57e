import PropTypes from 'prop-types';

const Lock = ({ color = '#A6A6A6' }) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M4.75 7.55556C4.75 3.82755 8.10992 1 12 1C15.8901 1 19.25 3.82755 19.25 7.55556V8.9065C19.6453 8.97095 20.0135 9.07096 20.365 9.23016C21.2475 9.62985 21.9872 10.2777 22.4589 11.1005C22.7631 11.6312 22.887 12.2024 22.9447 12.8303C23 13.4321 23 14.1708 23 15.0648V16.713C23 17.607 23 18.3457 22.9447 18.9475C22.887 19.5754 22.7631 20.1466 22.4589 20.6773C21.9872 21.5 21.2475 22.1479 20.365 22.5476C19.8104 22.7988 19.214 22.9027 18.5354 22.952C17.8738 23 17.0559 23 16.0384 23H7.96164C6.94406 23 6.12624 23 5.46457 22.952C4.78596 22.9027 4.18965 22.7988 3.63497 22.5476C2.75249 22.1479 2.01277 21.5 1.54115 20.6773C1.23694 20.1466 1.113 19.5754 1.05529 18.9475C0.999971 18.3457 0.999984 17.607 1 16.713V15.0648C0.999984 14.1708 0.999971 13.4321 1.05529 12.8303C1.113 12.2024 1.23694 11.6312 1.54115 11.1005C2.01277 10.2777 2.75249 9.62985 3.63497 9.23016C3.98648 9.07096 4.3547 8.97095 4.75 8.9065V7.55556ZM6.75 8.78166C7.11889 8.77777 7.52161 8.77777 7.9617 8.77778H16.0383C16.4784 8.77777 16.8811 8.77777 17.25 8.78166V7.55556C17.25 5.14707 15.0135 3 12 3C8.98653 3 6.75 5.14707 6.75 7.55556V8.78166ZM5.60944 10.8206C5.03742 10.8621 4.7076 10.9399 4.46011 11.052C3.93137 11.2915 3.52373 11.6635 3.2763 12.0951C3.17179 12.2774 3.09137 12.5295 3.04689 13.0133C3.00098 13.5128 3 14.1592 3 15.1111V16.6667C3 17.6186 3.00098 18.265 3.04689 18.7645C3.09137 19.2483 3.17179 19.5003 3.2763 19.6827C3.52373 20.1143 3.93137 20.4863 4.46011 20.7258C4.7076 20.8379 5.03742 20.9157 5.60944 20.9572C6.19012 20.9994 6.93522 21 8 21H16C17.0648 21 17.8099 20.9994 18.3906 20.9572C18.9626 20.9157 19.2924 20.8379 19.5399 20.7258C20.0686 20.4863 20.4763 20.1143 20.7237 19.6827C20.8282 19.5003 20.9086 19.2483 20.9531 18.7645C20.999 18.265 21 17.6186 21 16.6667V15.1111C21 14.1592 20.999 13.5128 20.9531 13.0133C20.9086 12.5295 20.8282 12.2774 20.7237 12.0951C20.4763 11.6635 20.0686 11.2915 19.5399 11.052C19.2924 10.9399 18.9626 10.8621 18.3906 10.8206C17.8099 10.7784 17.0648 10.7778 16 10.7778H8C6.93522 10.7778 6.19012 10.7784 5.60944 10.8206ZM13 13.7778V18H11V13.7778H13Z"
        fill={color}
      />
    </svg>
  );
};

Lock.propTypes = {
  color: PropTypes.string,
};

export default Lock;
