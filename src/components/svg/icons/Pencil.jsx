import PropTypes from 'prop-types';

const Pencil = ({ color = '#1B1B1B', width = '24', height = '24' }) => (
  <svg
    width={width}
    height={height}
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M17.2929 1.29289C17.6834 0.902369 18.3166 0.902369 18.7071 1.29289L22.7071 5.29289C23.0977 5.68342 23.0977 6.31658 22.7071 6.70711C22.3166 7.09763 21.6834 7.09763 21.2929 6.70711L17.2929 2.70711C16.9024 2.31658 16.9024 1.68342 17.2929 1.29289ZM14.5365 4.28052C14.8378 4.18264 15.1623 4.18264 15.4635 4.28052C15.6962 4.35611 15.8648 4.48165 15.9759 4.57595C16.0717 4.65732 16.1723 4.75794 16.2564 4.8422C16.262 4.84773 16.2674 4.8532 16.2728 4.85858L19.1578 7.7436C19.2421 7.82779 19.3427 7.92833 19.4241 8.02417C19.5184 8.13524 19.6439 8.30384 19.7195 8.53647C19.8174 8.83774 19.8174 9.16226 19.7195 9.46353C19.6439 9.69616 19.5184 9.86476 19.4241 9.97583C19.3427 10.0717 19.2421 10.1723 19.1578 10.2565L8.80803 20.6062C8.79535 20.6189 8.78278 20.6315 8.77031 20.644C8.58363 20.8309 8.41887 20.9958 8.22797 21.1349C8.05999 21.2573 7.87974 21.3619 7.69012 21.447C7.47465 21.5438 7.24969 21.605 6.99479 21.6743C6.97776 21.679 6.96059 21.6836 6.94328 21.6884L2.26314 22.9648C1.91693 23.0592 1.54667 22.9609 1.29292 22.7071C1.03917 22.4534 0.940838 22.0831 1.03526 21.7369L2.31166 17.0567C2.31638 17.0394 2.32105 17.0223 2.32569 17.0052C2.39504 16.7503 2.45624 16.5254 2.55298 16.3099C2.63811 16.1203 2.74272 15.94 2.86511 15.7721C3.00419 15.5812 3.16914 15.4164 3.35605 15.2297C3.36855 15.2172 3.38113 15.2047 3.39382 15.192L13.7272 4.85858C13.7326 4.8532 13.7381 4.84775 13.7436 4.84222C13.8278 4.75796 13.9283 4.65733 14.0242 4.57595C14.1353 4.48166 14.3039 4.35611 14.5365 4.28052ZM15 6.41421L4.80803 16.6062C4.56296 16.8513 4.51699 16.9012 4.48157 16.9498C4.44077 17.0058 4.4059 17.0659 4.37753 17.1291C4.35289 17.1839 4.33238 17.2486 4.24119 17.583L3.42524 20.5748L6.41704 19.7588C6.75142 19.6676 6.81609 19.6471 6.87096 19.6225C6.93415 19.5941 6.99424 19.5593 7.05024 19.5185C7.09885 19.483 7.14875 19.4371 7.39382 19.192L17.5858 9L15 6.41421Z"
      fill={color}
    />
  </svg>
);

Pencil.propTypes = {
  color: PropTypes.string,
  width: PropTypes.string,
  height: PropTypes.string,
};

export default Pencil;
