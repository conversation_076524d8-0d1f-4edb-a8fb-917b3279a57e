import PropTypes from 'prop-types';

const CircleUnion = ({ width = '14', height = '15', color = '#1B1B1B' }) => (
  <svg
    width={width}
    height={height}
    viewBox="0 0 14 15"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clip-path="url(#clip0_770_2844)">
      <path
        d="M7 0.5C10.866 0.5 14 3.63401 14 7.5C14 11.366 10.866 14.5 7 14.5C3.13401 14.5 0 11.366 0 7.5C0 3.63401 3.13401 0.5 7 0.5ZM7 2.5C4.23858 2.5 2 4.73858 2 7.5C2 10.2614 4.23858 12.5 7 12.5C9.76142 12.5 12 10.2614 12 7.5C12 4.73858 9.76142 2.5 7 2.5ZM7 5.5C8.10457 5.5 9 6.39543 9 7.5C9 8.60457 8.10457 9.5 7 9.5C5.89543 9.5 5 8.60457 5 7.5C5 6.39543 5.89543 5.5 7 5.5Z"
        fill={color}
      />
    </g>
    <defs>
      <clipPath id="clip0_770_2844">
        <rect
          width="14"
          height="14"
          fill="white"
          transform="translate(0 0.5)"
        />
      </clipPath>
    </defs>
  </svg>
);

CircleUnion.propTypes = {
  width: PropTypes.string,
  height: PropTypes.string,
  color: PropTypes.string,
};

export default CircleUnion;
