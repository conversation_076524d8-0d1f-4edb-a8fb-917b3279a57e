import PropTypes from 'prop-types';

const Add = ({ color = '#1B1B1B' }) => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M11 11V5H13V11H19V13H13V19H11V13H5V11H11Z"
      fill={color}
    />
  </svg>
);

Add.propTypes = {
  color: PropTypes.string,
};

export default Add;
