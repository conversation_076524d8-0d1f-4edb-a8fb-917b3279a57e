import PropTypes from 'prop-types';

const Pod = ({ color = '#6B6B6B' }) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M12.4241 20.9857L19.6418 17.5958C19.9927 17.431 20.2167 17.0783 20.2167 16.6907V7.15469C20.2167 6.76161 19.9864 6.40498 19.6281 6.24325L12.4104 2.98505C12.1489 2.86696 11.8491 2.86696 11.5876 2.98505L4.36981 6.24328C4.01154 6.40501 3.78125 6.76164 3.78125 7.15472L3.78131 16.6907C3.78131 17.0783 4.00533 17.431 4.35619 17.5958L11.5739 20.9857C11.8432 21.1122 12.1548 21.1122 12.4241 20.9857ZM1.78131 16.6907C1.78132 17.8536 2.45337 18.9117 3.50596 19.4061L10.7237 22.796C11.5316 23.1755 12.4665 23.1755 13.2744 22.796L20.492 19.4061C21.5446 18.9117 22.2167 17.8536 22.2167 16.6907V7.15469C22.2167 5.97544 21.5258 4.90557 20.451 4.42038L13.2333 1.16217C12.4486 0.807921 11.5494 0.807921 10.7647 1.16217L3.54693 4.42041C2.47211 4.9056 1.78124 5.97548 1.78125 7.15473L1.78131 16.6907Z"
        fill={color}
      />
      <path
        d="M11.9991 5.99987L12.3431 6.92939C13.1533 9.11915 14.8798 10.8456 17.0696 11.6559L17.9991 11.9999L17.0696 12.3438C14.8798 13.1541 13.1533 14.8806 12.3431 17.0703L11.9991 17.9999L11.6552 17.0703C10.8449 14.8806 9.11839 13.1541 6.92863 12.3438L5.99911 11.9999L6.92863 11.6559C9.11838 10.8456 10.8449 9.11915 11.6552 6.92939L11.9991 5.99987Z"
        fill={color}
      />
    </svg>
  );
};

Pod.propTypes = {
  color: PropTypes.string,
};

export default Pod;
