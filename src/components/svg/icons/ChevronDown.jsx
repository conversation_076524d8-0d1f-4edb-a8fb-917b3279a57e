import PropTypes from 'prop-types';

const ChevronDown = ({ color = '#6B6B6B' }) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M6.29285 9.70718L7.70706 8.29297L12 12.5859L16.2928 8.29297L17.7071 9.70718L12 15.4143L6.29285 9.70718Z"
        fill={color}
      />
    </svg>
  );
};

ChevronDown.propTypes = {
  color: PropTypes.string,
};

export default ChevronDown;
