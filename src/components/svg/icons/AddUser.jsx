import PropTypes from 'prop-types';

const AddUser = ({ color = '#1B1B1B' }) => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M14.9483 1.98859L15.8752 2.3638C17.7058 3.10479 19 4.90004 19 6.99998C19 9.09992 17.7058 10.8952 15.8752 11.6362L14.9483 12.0114L14.1978 10.1575L15.1248 9.78228C16.2261 9.3365 17 8.25742 17 6.99998C17 5.74254 16.2261 4.66346 15.1248 4.21768L14.1978 3.84246L14.9483 1.98859ZM9.5 3.99998C7.84315 3.99998 6.5 5.34313 6.5 6.99998C6.5 8.65683 7.84315 9.99998 9.5 9.99998C11.1569 9.99998 12.5 8.65683 12.5 6.99998C12.5 5.34313 11.1569 3.99998 9.5 3.99998ZM4.5 6.99998C4.5 4.23856 6.73858 1.99998 9.5 1.99998C12.2614 1.99998 14.5 4.23856 14.5 6.99998C14.5 9.7614 12.2614 12 9.5 12C6.73858 12 4.5 9.7614 4.5 6.99998ZM7.96448 14H13V16H8C7.05444 16 6.39534 16.0005 5.87945 16.0357C5.37254 16.0703 5.07733 16.135 4.85195 16.2283C4.11687 16.5328 3.53284 17.1168 3.22836 17.8519C3.135 18.0773 3.07033 18.3725 3.03574 18.8794C3.00054 19.3953 3 20.0544 3 21V22H1L1 20.9645C0.999993 20.0627 0.999988 19.3353 1.04038 18.7433C1.08191 18.1345 1.16948 17.5963 1.3806 17.0866C1.88807 15.8614 2.86144 14.8881 4.08658 14.3806C4.59628 14.1695 5.13456 14.0819 5.74331 14.0404C6.33531 14 7.06272 14 7.96448 14ZM20 14V17H23V19H20V22H18V19H15V17H18V14H20Z"
      fill={color}
    />
  </svg>
);

AddUser.propTypes = {
  color: PropTypes.string,
};

export default AddUser;
