import PropTypes from 'prop-types';

const Alert = ({ width = '24', height = '24', color = '#912E2E' }) => (
  <svg
    width={width}
    height={height}
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M2.73808 17.9979C1.96615 19.3312 2.92827 21 4.46894 21H19.5311C21.0718 21 22.0339 19.3312 21.262 17.9979L13.7309 4.98962C12.9606 3.65905 11.0395 3.65906 10.2692 4.98963L2.73808 17.9979ZM13 18H11V16H13V18ZM13 14H11V9.99997H13V14Z"
      fill={color}
    />
  </svg>
);

Alert.propTypes = {
  width: PropTypes.string,
  height: PropTypes.string,
  color: PropTypes.string,
};

export default Alert;
