import PropTyoes from 'prop-types';

const Close = ({ color = '#1B1B1B' }) => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M4.29297 5.70712L5.70718 4.29291L12.0001 10.5858L18.293 4.29291L19.7072 5.70712L12.0001 13.4142L4.29297 5.70712Z"
      fill={color}
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M19.707 18.2929L18.2928 19.7071L11.9999 13.4142L5.70703 19.7071L4.29282 18.2929L11.9999 10.5858L19.707 18.2929Z"
      fill={color}
    />
  </svg>
);

Close.propTypes = {
  color: PropTyoes.string,
};

export default Close;
