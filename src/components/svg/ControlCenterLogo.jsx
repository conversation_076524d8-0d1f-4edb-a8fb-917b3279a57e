const ControlCenterLogo = () => {
  return (
    <svg
      width="142"
      height="18"
      viewBox="0 0 242 30"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g id="Control Center Logo 1" clipPath="url(#clip0_2728_166)">
        <path
          id="Vector"
          fillRule="evenodd"
          clipRule="evenodd"
          d="M45.3317 8.17849V11.6159C44.4878 11.1837 43.1704 10.6279 41.4003 10.6279C38.6009 10.6279 36.9748 12.6039 36.9748 14.8887C36.9748 17.194 38.5598 19.1494 41.2356 19.1494C43.1293 19.1494 44.5495 18.6142 45.4346 18.1614V21.64C44.529 22.1134 42.8411 22.5868 40.9063 22.5868C36.4603 22.5868 33.1875 19.2112 33.1875 14.8887C33.1875 10.6073 36.4603 7.19049 41.0709 7.19049C43.0469 7.19049 44.426 7.70508 45.3317 8.17849ZM49.1942 14.8887C49.1942 10.6485 52.4669 7.19049 57.0365 7.19049C61.606 7.19049 64.8787 10.6279 64.8787 14.8887C64.8787 19.1906 61.6266 22.5868 57.0365 22.5868C52.4669 22.5868 49.1942 19.1906 49.1942 14.8887ZM52.9815 14.8887C52.9815 17.194 54.6282 19.1494 57.0365 19.1494C59.4447 19.1494 61.0708 17.194 61.0708 14.8887C61.0708 12.6039 59.4447 10.6279 57.0365 10.6279C54.6282 10.6279 52.9815 12.6039 52.9815 14.8887ZM81.9364 22.5045H82.0805V7.47866H78.3754V14.9504C78.1389 14.7731 77.9094 14.5754 77.6787 14.3768C77.5079 14.2297 77.3362 14.082 77.161 13.9418L69.6275 7.27282H69.4628V22.2987H73.1473V14.8269L74.3411 15.8149L81.9364 22.5045ZM97.9364 10.6485H93.9843V22.2987H90.197V10.6485H86.245V7.47866H97.9364V10.6485ZM107.513 7.47866H102.12V22.2987H105.805V17.4204H107.451L109.839 22.2987H113.77L110.786 16.8029C112.412 16.0825 113.503 14.6623 113.503 12.5216C113.503 9.18708 111.053 7.47866 107.513 7.47866ZM105.805 10.6485H107.678C108.81 10.6485 109.695 11.2248 109.695 12.5216C109.695 13.8801 108.83 14.4153 107.698 14.4153H105.805V10.6485ZM117.403 14.8887C117.403 10.6485 120.676 7.19049 125.246 7.19049C129.815 7.19049 133.088 10.6279 133.088 14.8887C133.088 19.1906 129.836 22.5868 125.246 22.5868C120.676 22.5868 117.403 19.1906 117.403 14.8887ZM121.191 14.8887C121.191 17.194 122.837 19.1494 125.246 19.1494C127.654 19.1494 129.28 17.194 129.28 14.8887C129.28 12.6039 127.654 10.6279 125.246 10.6279C122.837 10.6279 121.191 12.6039 121.191 14.8887ZM137.672 22.2987H146.791V19.1288H141.439V7.47866H137.672V22.2987ZM163.974 10.1749V8.30186C163.48 8.01369 161.813 7.23152 159.57 7.23152C155.082 7.23152 151.768 10.4837 151.768 14.8885C151.768 19.2933 155.082 22.5455 159.57 22.5455C161.916 22.5455 163.583 21.7839 164.077 21.4957V19.6227C163.11 20.1167 161.566 20.7136 159.57 20.7136C156.194 20.7136 153.765 18.2436 153.765 14.8885C153.765 11.5335 156.194 9.06344 159.57 9.06344C161.463 9.06344 163.007 9.68095 163.974 10.1749ZM169.189 22.2985H178.595V20.6312H171.165V15.6295H177.175V13.9623H171.165V9.14578H178.287V7.47852H169.189V22.2985ZM195.58 22.5455H195.559L186.359 12.5626L185.885 12.048V22.2985H183.909V7.23152H183.93L193.131 17.2144L193.604 17.729V7.47852H195.58V22.5455ZM206.762 9.14578H211.29V7.47852H200.278V9.14578H204.786V22.2985H206.762V9.14578ZM225.39 22.2985H215.983V7.47852H225.081V9.14578H217.959V13.9623H223.97V15.6295H217.959V20.6312H225.39V22.2985ZM235.5 7.47852H230.704V22.2985H232.68V16.4941H235.767L238.834 22.2985H241.037L237.682 16.1647C239.267 15.6707 240.707 14.2299 240.707 12.0069C240.707 9.16636 238.752 7.47852 235.5 7.47852ZM232.68 9.14578H235.438C237.496 9.14578 238.731 10.2161 238.731 12.0069C238.731 13.7976 237.558 14.868 235.603 14.868H232.68V9.14578Z"
          fill="white"
        />
        <path
          id="Union"
          fillRule="evenodd"
          clipRule="evenodd"
          d="M14.5799 0C15.6091 2.78137 17.8021 4.97429 20.5834 6.00349C17.8021 7.0327 15.6091 9.22562 14.5799 12.007C13.5507 9.22562 11.3578 7.0327 8.57644 6.00349C11.3578 4.97429 13.5507 2.78137 14.5799 0ZM0.857666 14.5799C0.857666 22.1585 7.00133 28.3022 14.5799 28.3022C22.1586 28.3022 28.3022 22.1585 28.3022 14.5799H24.8716C24.8716 20.2638 20.2639 24.8716 14.5799 24.8716C8.89598 24.8716 4.28823 20.2638 4.28823 14.5799H0.857666ZM7.7188 14.5799C7.7188 13.2266 8.11057 11.9649 8.78687 10.9019C9.67366 11.7299 10.5184 12.6021 11.3177 13.5154C11.2084 13.8505 11.1494 14.2083 11.1494 14.5799C11.1494 16.4745 12.6853 18.0104 14.5799 18.0104C16.4746 18.0104 18.0105 16.4745 18.0105 14.5799C18.0105 14.0736 17.9009 13.593 17.704 13.1604C18.4637 12.2477 19.2867 11.3907 20.1663 10.5955C20.9688 11.7187 21.4411 13.0942 21.4411 14.5799C21.4411 18.3692 18.3692 21.4409 14.5799 21.4409C10.7906 21.4409 7.7188 18.3692 7.7188 14.5799Z"
          fill="url(#paint0_radial_2728_166)"
        />
      </g>
      <defs>
        <radialGradient
          id="paint0_radial_2728_166"
          cx="0"
          cy="0"
          r="1"
          gradientUnits="userSpaceOnUse"
          gradientTransform="translate(24.2514 6.71541) rotate(138.315) scale(22.2023 21.9282)"
        >
          <stop stopColor="#2E3191" />
          <stop offset="1" stopColor="#00B8F1" />
        </radialGradient>
        <clipPath id="clip0_2728_166">
          <rect width="241.855" height="29.1598" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
};

export default ControlCenterLogo;
