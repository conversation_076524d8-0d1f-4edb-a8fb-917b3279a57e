import { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import { DateTime } from 'luxon';
import { useAsyncValue, useOutletContext } from 'react-router-dom';
import _ from 'lodash';

import API from '../../utils/api.js';

import {
  useSchedule,
  useScheduleDispatch,
  SCHEDULE_ACTIONS,
} from '../../contexts/ScheduleContext.jsx';
import { DSPopover, DSButton, DSToastMessage } from '../../components';
import { DSScheduleDetails } from '../schedule';
import {
  TOAST_ACTIONS,
  useToastContext,
} from '../../contexts/ToastMessageContext.jsx';

const DSSchedulePopover = ({ showModal, onClose }) => {
  const {
    selectedPod,
    selectedStartDate,
    selectedStartTime,
    selectedEndDate,
    selectedEndTime,
    selectedLocation,
    isCustomLocation,
    podDropdownOptions,
    addressName,
    locationInfo,
    driverNotes,
  } = useSchedule();
  const { userData } = useOutletContext();
  const { allSites } = useAsyncValue();
  const siteInfo = {
    site_id: userData?.roles[0]?.site_id,
    tz: allSites[0]?.tz,
  };
  const dispatch = useScheduleDispatch();
  const { toastState, toastDispatch } = useToastContext();
  const [submittingSchedule, setSubmittingSchedule] = useState(false);

  const { getSiteGroupLocations, getMobileSites, createSchedule } = API;

  useEffect(() => {
    const getMobileSiteList = async () => {
      let mobileSites = await getMobileSites(siteInfo?.site_id);
      const updatedMobileSites = mobileSites.map((site) => ({
        ...site,
        label: site.site_id, // added a common key for Select picker
      }));
      dispatch({
        type: SCHEDULE_ACTIONS.UPDATE_POD_DROPDOWN,
        payload: {
          podOptions: updatedMobileSites,
          selectedPod,
        },
      });
    };
    const getSiteLocations = async () => {
      let locations = await getSiteGroupLocations(siteInfo?.site_id);
      locations.push({ name: 'Custom Location' });
      const updatedLocations = locations.map((location) => ({
        ...location,
        label: location.name, // added a common key for Select picker
      }));
      dispatch({
        type: SCHEDULE_ACTIONS.UPDATE_LOCATION_DROPDOWN,
        payload: {
          locationOptions: updatedLocations,
          selectedLocation,
        },
      });
    };
    getMobileSiteList();
    getSiteLocations();
  }, [showModal]);

  const handlePodSelection = (pod) => {
    dispatch({
      type: SCHEDULE_ACTIONS.UPDATE_POD_DROPDOWN,
      payload: {
        podOptions: podDropdownOptions,
        selectedPod: pod,
      },
    });
  };

  const handleScheduledStartDateChange = (date) => {
    const selectedDate = DateTime.fromJSDate(date);
    dispatch({
      type: SCHEDULE_ACTIONS.SET_START_DATE,
      payload: { selectedStartDate: selectedDate },
    });
  };

  const handleScheduledEndDateChange = (date) => {
    const selectedDate = DateTime.fromJSDate(date);
    dispatch({
      type: SCHEDULE_ACTIONS.SET_END_DATE,
      payload: { selectedEndDate: selectedDate },
    });
  };

  const handleScheduledStartTimeChange = (time) => {
    const selectedTime = DateTime.fromJSDate(time);
    const newTime = DateTime.fromISO(selectedTime);
    const newSelectedStartTime = selectedStartDate?.set({
      hour: newTime.hour,
      minute: newTime.minute,
      second: newTime.second,
    });

    dispatch({
      type: SCHEDULE_ACTIONS.SET_START_TIME,
      payload: { selectedStartTime: newSelectedStartTime },
    });
  };

  const handleScheduledEndTimeChange = (time) => {
    const selectedTime = DateTime.fromJSDate(time);
    const newTime = DateTime.fromISO(selectedTime);
    const newSelectedEndTime = selectedEndDate?.set({
      hour: newTime.hour,
      minute: newTime.minute,
      second: newTime.second,
    });
    dispatch({
      type: SCHEDULE_ACTIONS.SET_END_TIME,
      payload: { selectedEndTime: newSelectedEndTime },
    });
  };

  const handleOnSelectLocation = (location) => {
    dispatch({
      type: SCHEDULE_ACTIONS.SET_LOCATION,
      payload: {
        selectedLocation: location,
      },
    });
  };

  const onChangeAddressName = (e) => {
    dispatch({
      type: SCHEDULE_ACTIONS.SET_ADDRESS_NAME,
      payload: {
        addressName: e.target.value,
      },
    });
  };

  const onChangeLocationInfo = (e) => {
    dispatch({
      type: SCHEDULE_ACTIONS.SET_LOCATION_INFO,
      payload: {
        locationInfo: e.target.value,
      },
    });
  };

  const onChangeDriverNotes = (e) => {
    dispatch({
      type: SCHEDULE_ACTIONS.SET_DRIVER_NOTES,
      payload: {
        driverNotes: e.target.value,
      },
    });
  };

  const onSubmitSchedule = async () => {
    // Create DateTime in a specific timezone
    const zonedStartTime = DateTime.fromISO(selectedStartTime, {
      zone: siteInfo?.tz,
    });
    const zonedEndTime = DateTime.fromISO(selectedEndTime, {
      zone: siteInfo?.tz,
    });

    const reqData = {
      start_timestamp: zonedStartTime.toUTC().toISO(),
      available_start_timestamp: zonedStartTime.toUTC().toISO(),
      end_timestamp: zonedEndTime.toUTC().toISO(),
      available_end_timestamp: zonedEndTime.toUTC().toISO(),
      site_group_location_id: isCustomLocation
        ? null
        : selectedLocation?.site_group_location_id,
      address: isCustomLocation ? addressName : null,
      location_info: isCustomLocation ? locationInfo : null,
      notes: driverNotes,
    };
    try {
      setSubmittingSchedule(true);
      let response = await createSchedule(
        siteInfo?.site_id,
        selectedPod?.site_id,
        reqData,
      );
      onClosePopover({
        variant: TOAST_ACTIONS.SHOW_TOAST_INFO,
        title: 'Schedule Created',
        description: `The schedule for ${selectedPod?.site_id} has been successfully added to the calendar.`,
      });
    } catch (e) {
      if (e?.status === 409) {
        toastDispatch({
          type: TOAST_ACTIONS.SHOW_TOAST_ERROR,
          payload: {
            title: 'Notice - Cannot Create Schedule',
            description:
              'This schedule conflicts with another existing schedule for this Mobile Resource. Please try a different date in order to continue creating this schedule.',
          },
        });
      } else {
        toastDispatch({
          type: TOAST_ACTIONS.SHOW_TOAST_ERROR,
          payload: {
            title: 'Notice - Cannot Create Schedule',
            description: e?.message ?? '',
          },
        });
      }
    } finally {
      setSubmittingSchedule(false);
    }
  };

  const onClosePopover = (toastInfo) => {
    onClose(toastInfo);
    toastDispatch({ type: 'DISMISS_TOAST' });
    dispatch({
      type: SCHEDULE_ACTIONS.RESET,
    });
  };

  const handleFooterButton = () => {
    handleDismissToast();
    onSubmitSchedule();
  };

  const calculateFooterButtonDisabled = () => {
    let isEnabled = false;
    if (
      selectedPod &&
      selectedStartDate &&
      selectedStartTime &&
      selectedEndDate &&
      selectedEndTime &&
      selectedLocation
    ) {
      isEnabled = true;
    }
    if (isEnabled && isCustomLocation) {
      if (!addressName || !locationInfo) {
        isEnabled = false;
      }
    }
    return isEnabled;
  };

  const getFooterText = () => {
    if (!selectedPod) {
      return 'Select a mobile resource';
    }

    if (
      !selectedStartDate ||
      !selectedStartTime ||
      !selectedEndDate ||
      !selectedEndTime
    ) {
      return 'Select a start & end time';
    }

    if (!selectedLocation || !addressName || !locationInfo) {
      return 'Select a location';
    }

    const startTime = DateTime.fromISO(selectedStartTime, {
      zone: siteInfo?.tz,
    })
      .toFormat('EEE, MMM d yyyy, h:mm a')
      .toUpperCase();
    const endTime = DateTime.fromISO(selectedEndTime, { zone: siteInfo?.tz })
      .toFormat('EEE, MMM d yyyy, h:mm a')
      .toUpperCase();

    return `${startTime} - ${endTime}`;
  };

  const getHeading = () => {
    return 'Schedule Details';
  };

  const handleDismissToast = () => {
    toastDispatch({ type: 'DISMISS_TOAST' });
  };

  const renderPage = () => {
    return (
      <DSScheduleDetails
        handlePodSelection={handlePodSelection}
        handleScheduledStartDateChange={handleScheduledStartDateChange}
        handleScheduledEndDateChange={handleScheduledEndDateChange}
        handleScheduledStartTimeChange={handleScheduledStartTimeChange}
        handleScheduledEndTimeChange={handleScheduledEndTimeChange}
        handleOnSelectLocation={handleOnSelectLocation}
        onChangeAddressName={onChangeAddressName}
        onChangeLocationInfo={onChangeLocationInfo}
        onChangeDriverNotes={onChangeDriverNotes}
      />
    );
  };

  const footerButton = (
    <DSButton
      label="Create Schedule"
      variant="primary"
      onClick={handleFooterButton}
      disabled={!calculateFooterButtonDisabled()}
      loading={submittingSchedule}
    />
  );

  const renderToastAlert = () => {
    return (
      <DSToastMessage
        title={toastState.title}
        description={toastState.description}
        variant={toastState.variant}
        onDismiss={handleDismissToast}
      />
    );
  };

  return (
    <DSPopover
      showModal={showModal}
      onClose={() => onClosePopover()}
      content={renderPage()}
      subHeading="New Mobile Resource Schedule"
      heading={getHeading()}
      showBackButton={false}
      footerButton={footerButton}
      footerInfo={getFooterText()}
      renderToastMessage={renderToastAlert()}
      showToastMessage={toastState.isShowing}
    />
  );
};

DSSchedulePopover.propTypes = {
  showModal: PropTypes.bool,
  onClose: PropTypes.func,
};

export default DSSchedulePopover;
