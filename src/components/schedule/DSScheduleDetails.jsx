import { forwardRef } from 'react';
import PropTypes from 'prop-types';
import styled from 'styled-components';
import tw from 'twin.macro';
import { DateTime } from 'luxon';

import { useSchedule } from '../../contexts/ScheduleContext';

import {
  DSButton,
  DSInput,
  DSDatePicker,
  DSSelectDropdown,
  DSTextArea,
} from '../../components';

import MobilePodIcon from '../svg/icons/MobilePodIcon';
import HelpIcon from '../svg/icons/HelpIcon';
import SmartTimeIcon from '../svg/icons/StartTimeIcon';
import EndTimeIcon from '../svg/icons/EndTimeIcon';
import LocationIcon from '../svg/icons/LocationIcon';
import NotesIcon from '../svg/icons/NotesIcon';
import DSTimePicker from '../DSTimePicker.jsx';

const StyledDSScheduleDetails = styled.div`
  ${tw`relative`};

  .divider {
    ${tw`
        border-t
        border-solid
        border-dslGray-2
      `}
  }
`;

const StyledSessionBody = styled.div`
  ${tw`
      relative
      flex flex-col
      px-10 py-2.5
      gap-8
      last:pb-0
    `};

  .address-container {
    ${tw`
            flex-[1]
            h-[150px]
            `}

    .address-title {
      ${tw`
          text-dslGray-5
          font-light
          text-xs
          leading-[24px]
        `}
    }
    .address-description {
      ${tw`
          text-dslGray-4
          font-light
          text-xs
          leading-[24px]
        `}
    }
  }

  .header-description {
    ${tw`
          flex bg-dslGray-2 items-center px-2 py-2
        `}

    .header-description-text {
      ${tw`
          text-black
          font-light
          text-sm
          leading-[20px]
        `}
    }
  }

  .header-row {
    ${tw`
        flex
        items-center
        justify-between
      `}

    .header-row-header-text {
      ${tw`
          text-dslGray-4
          uppercase
          font-medium
          leading-[24px]
        `}
    }
  }
`;

const StyledSessionRow = styled.div`
  ${tw`
      relative
      flex flex-row
      gap-4
      items-center
    `};
`;

const CustomDateButton = forwardRef(
  ({ value, onClick, isOpen, disabled, placeholder }, ref) => {
    const date = DateTime.fromFormat(value, 'LL/dd/yyyy');

    return (
      <DSButton
        ref={ref}
        label={value ? date.toFormat('EEE, MMM d') : placeholder}
        variant="outline"
        onClick={onClick}
        selected={isOpen}
        disabled={disabled}
        fullWidth
      />
    );
  },
);

CustomDateButton.displayName = 'CustomDateButton';
CustomDateButton.propTypes = {
  value: PropTypes.string,
  onClick: PropTypes.func,
  isOpen: PropTypes.bool,
  disabled: PropTypes.bool,
  placeholder: PropTypes.string,
};

const DSScheduleDetails = ({
  handlePodSelection,
  handleScheduledStartDateChange,
  handleScheduledEndDateChange,
  handleScheduledStartTimeChange,
  handleScheduledEndTimeChange,
  handleOnSelectLocation,
  onChangeAddressName,
  onChangeLocationInfo,
  onChangeDriverNotes,
}) => {
  // const { siteInfo, pods } = useAsyncValue();
  const {
    selectedPod,
    selectedStartDate,
    selectedStartTime,
    selectedEndDate,
    selectedEndTime,
    selectedLocation,
    isCustomLocation,
    podDropdownOptions,
    locationDropdownOptions,
    addressName,
    locationInfo,
    driverNote,
  } = useSchedule();

  function isToday(input) {
    if (!input) return false;
    const date = DateTime.fromISO(input);
    const today = DateTime.local();
    return date.hasSame(today, 'day');
  }

  function isSameDate(d1, d2) {
    if (!d1 || !d2) return false;

    const date1 = DateTime.fromISO(d1); // or fromJSDate(d1) if input is a JS Date
    const date2 = DateTime.fromISO(d2);

    return date1.hasSame(date2, 'day'); // ✅ checks same calendar day
  }

  return (
    <StyledDSScheduleDetails>
      <StyledSessionBody>
        <div className="header-row">
          <div className="header-row-header-text">Mobile Resource</div>
        </div>
        <StyledSessionRow>
          <div>
            <MobilePodIcon />
          </div>
          <div tw="flex w-full">
            <DSSelectDropdown
              options={podDropdownOptions}
              placeholder="Select Mobile Resource"
              onChangeSelection={handlePodSelection}
              selectedOption={selectedPod?.site_id}
              variant="light"
            />
          </div>
        </StyledSessionRow>
      </StyledSessionBody>
      <div tw="pb-4" />
      <div className="divider" />
      <StyledSessionBody>
        <div className="header-row">
          <div className="header-row-header-text">Start & End Time</div>
        </div>
        <div className="header-description">
          <div tw="px-2">
            <HelpIcon />
          </div>
          <div className="header-description-text">
            The start and end times define the period during which this mobile
            resource is available for booking. Reservations are only permitted
            within this specified timeframe.
          </div>
        </div>
        <StyledSessionRow>
          <div>
            <SmartTimeIcon />
          </div>
          <div tw="w-[100px] px-2">Start Time</div>
          <div tw="flex-1">
            <DSDatePicker
              placeholder="Select Start Date"
              customInput={CustomDateButton}
              selectedDate={selectedStartDate?.toJSDate()}
              onChange={handleScheduledStartDateChange}
              minDate={DateTime.now()}
              disabled={!selectedPod}
            />
          </div>
          <div tw="flex-1">
            <DSTimePicker
              date={selectedStartDate}
              placeholder="Select Start Time..."
              selectedTime={selectedStartTime?.toJSDate()}
              onChange={handleScheduledStartTimeChange}
              onBlur={handleScheduledStartTimeChange}
              disabled={!selectedStartDate}
              minTime={
                isToday(selectedStartDate)
                  ? DateTime.now()
                  : selectedStartDate?.startOf('day')
              }
              maxTime={selectedStartDate?.endOf('day')}
            />
          </div>
        </StyledSessionRow>
        <StyledSessionRow>
          <div>
            <EndTimeIcon />
          </div>
          <div tw="w-[100px] px-2">End Time</div>
          <div tw="flex-1">
            <DSDatePicker
              placeholder="Select End Date"
              customInput={CustomDateButton}
              selectedDate={selectedEndDate?.toJSDate()}
              onChange={handleScheduledEndDateChange}
              minDate={selectedStartDate?.toJSDate()}
              disabled={!selectedStartTime}
            />
          </div>
          <div tw="flex-1">
            <DSTimePicker
              placeholder="Select End Time..."
              selectedTime={selectedEndTime?.toJSDate()}
              onChange={handleScheduledEndTimeChange}
              onBlur={handleScheduledEndTimeChange}
              disabled={!selectedEndDate}
              minTime={
                isSameDate(selectedStartDate, selectedEndDate)
                  ? selectedStartTime?.plus({ minutes: 15 })
                  : selectedEndDate?.startOf('day')
              }
              maxTime={selectedEndDate?.endOf('day')}
            />
          </div>
        </StyledSessionRow>
      </StyledSessionBody>
      <div tw="pb-4" />
      <div className="divider" />
      <StyledSessionBody>
        <div className="header-row">
          <div className="header-row-header-text">Location Details</div>
        </div>
        <StyledSessionRow>
          <div>
            <LocationIcon />
          </div>
          <div tw="flex w-full">
            <DSSelectDropdown
              options={locationDropdownOptions}
              placeholder="Select Location"
              onChangeSelection={handleOnSelectLocation}
              selectedOption={selectedLocation?.name}
              variant="light"
              disabled={!selectedPod}
            />
          </div>
        </StyledSessionRow>
        <StyledSessionRow>
          <div className="address-container">
            <div className="address-title">Address</div>
            <div tw="">
              <DSInput
                placeholder=""
                value={addressName}
                disabled={!isCustomLocation}
                onChange={onChangeAddressName}
              />
            </div>
            <div className="address-description">
              The address is pre-determined by the location selected. Select
              Custom Location in order to enter a different address.
            </div>
          </div>
          <div className="address-container">
            <div className="address-title">Location</div>
            <div tw="">
              <DSInput
                placeholder=""
                value={locationInfo}
                disabled={!isCustomLocation}
                onChange={onChangeLocationInfo}
              />
            </div>
          </div>
        </StyledSessionRow>
      </StyledSessionBody>
      <div tw="pb-4" />
      <div className="divider" />
      <StyledSessionBody>
        <div className="header-row">
          <div className="header-row-header-text">Notes</div>
        </div>
        <StyledSessionRow>
          <div>
            <NotesIcon />
          </div>
          <div className="address-container">
            <div className="address-title">Driver Notes</div>
            <div tw="h-[112px]">
              <DSTextArea
                value={driverNote}
                onChange={onChangeDriverNotes}
                placeholder=""
              />
            </div>
          </div>
        </StyledSessionRow>
      </StyledSessionBody>
      <div tw="pb-4" />
    </StyledDSScheduleDetails>
  );
};

DSScheduleDetails.propTypes = {
  handlePodSelection: PropTypes.func,
  handleScheduledStartDateChange: PropTypes.func,
  handleScheduledEndDateChange: PropTypes.func,
  handleScheduledStartTimeChange: PropTypes.func,
  handleScheduledEndTimeChange: PropTypes.func,
  handleOnSelectLocation: PropTypes.func,
  onChangeAddressName: PropTypes.func,
  onChangeLocationInfo: PropTypes.func,
  onChangeDriverNotes: PropTypes.func,
};

export default DSScheduleDetails;
