import styled from 'styled-components';
import tw from 'twin.macro';
import PropTypes from 'prop-types';
import { DSButton } from '../components';

const StyledPageContainer = styled.div`
  ${tw`
    flex flex-col items-center
    h-auto
    bg-dslBlack-2
    text-dslWhite
    p-10
    min-h-full
    bg-transparent
  `};

  .bg-video-container {
    ${tw`
      fixed
      top-0 left-0 right-0 bottom-0
      -z-10
    `};

    .bg-dark-overlay {
      ${tw`
        w-screen h-screen
        absolute
        top-0 left-0
      `};
      background: linear-gradient(
        50deg,
        rgba(0, 0, 0, 0.52) 0%,
        rgba(0, 0, 0, 0.2) 100.6%
      );
    }

    .bg-video {
      ${tw`
        w-screen h-screen
        object-cover
        bg-center
      `};
    }
  }
`;

const StyledBrandingSection = styled.div`
  ${tw`
    flex flex-col
    flex-1
    items-center
    justify-center
    py-5 px-11
    text-center
  `};

  h1 {
    ${tw`
      text-9xl
      font-bold
      mt-5
    `};
  }

  h2 {
    ${tw`
      text-4xl
      mt-5
    `};
  }

  p {
    ${tw`
      text-xl
      mt-5
    `};
  }
`;

const getErrorMessage = (status) => {
  switch (status) {
    case 500:
      return 'The server encountered an error and could not complete your request.';
    case 401:
      return 'You are not authorized to view this page. Please log in.';
    case 404:
      return 'The page you are looking for could not be found.';
    case 403:
      return 'You do not have permission to access this resource.';
    case 400:
      return 'Bad request. Please check the request and try again.';
    case 503:
      return 'The server is temporarily unavailable. Please try again later.';
    default:
      return 'Something went wrong. Please try again.';
  }
};

const ErrorPage = ({ status, message }) => {
  const handleGoBack = () => {
    window.location.reload();
  };

  return (
    <StyledPageContainer>
      <div className="bg-video-container">
        <div className="bg-dark-overlay" />
      </div>
      <div tw="flex flex-col justify-center items-center h-screen">
        <StyledBrandingSection>
          <div>
            <h1>{status}</h1>
            <h2>{getErrorMessage(status)}</h2>
            <p>{message || 'An unexpected error occurred'}</p>
          </div>
          <div tw="mt-10">
            <DSButton
              variant="secondary"
              label={'Try again'}
              fullWidth
              onClick={handleGoBack}
            />
          </div>
        </StyledBrandingSection>
      </div>
    </StyledPageContainer>
  );
};

// Adding propTypes for validation
ErrorPage.propTypes = {
  status: PropTypes.oneOfType([PropTypes.number, PropTypes.string]).isRequired,
  message: PropTypes.string,
};

// Adding defaultProps in case the message prop is not provided
// ErrorPage.defaultProps = {
//   message: 'An unexpected error occurred.',
// };

export default ErrorPage;
