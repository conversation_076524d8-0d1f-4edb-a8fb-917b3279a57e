import { useState, forwardRef } from 'react';
import PropTypes from 'prop-types';
import DatePicker from 'react-datepicker';
import { DateTime } from 'luxon';
import styled from 'styled-components';
import tw from 'twin.macro';

import DSButton from './DSButton';
import DSButtonSmall from './DSButtonSmall';
import { ChevronLeft, Calendar } from './svg/icons';

import 'react-datepicker/dist/react-datepicker.css';

const StyledDatePickerContainer = styled.div`
  ${({ autoWidth }) => (autoWidth ? tw`w-auto` : tw`w-full`)}

  .date-outer-button {
    ${tw`
      w-full
      h-[40px] md:h-[54px]
      px-2 md:px-4
      py-1 md:py-3.5
      gap-1 md:gap-2.5
      text-xs
      sm:text-sm
      md:text-base
    `}
  }

  .react-datepicker {
    ${tw`
      relative
      font-sans
      px-2 md:px-6 py-5
      h-full
      w-full
    `};
  }

  .react-datepicker__month-container {
    ${tw`w-full h-full`}
  }

  .react-datepicker-wrapper,
  .react-datepicker__input-container {
    ${tw`w-full`}
  }

  .react-datepicker__month-text--in-range {
    ${tw`bg-dslWhite text-dslBlack-2`}
  }
  .react-datepicker__tab-loop {
    ${tw`relative right-36`}
  }

  .react-datepicker__triangle {
    ${tw`hidden`};
  }

  .react-datepicker__header {
    ${tw`
      bg-transparent
      border-none
    `};
  }

  .react-datepicker__month {
    ${({ type }) =>
      type === 'day'
        ? tw`flex flex-col gap-2 m-0`
        : tw`flex flex-wrap gap-2 m-0`}
  }

  .react-datepicker__month-wrapper {
    ${tw`flex gap-2`}
  }

  .react-datepicker__month-text {
    ${tw`
      w-[80px] h-[40px]
      flex items-center justify-center
      m-0
      text-sm
      hover:(bg-dslGray-1)
    `};

    &--selected {
      ${tw`
        bg-dslBlue-dark
        text-dslWhite
        font-bold
        hover:(bg-dslBlue-dark)
      `};
    }

    &--disabled {
      ${tw`
        pointer-events-none
        text-dslGray-3
      `};
    }
  }

  .react-datepicker__week {
    ${tw`flex gap-2`};
  }

  .react-datepicker__week:has(.react-datepicker__day--in-range) {
    ${tw`bg-dslBlue-light rounded-[3px]`}
  }

  .react-datepicker__day-names {
    ${tw`flex justify-between m-0`};

    .react-datepicker__day-name {
      ${tw`w-10 h-10 m-0 flex items-center justify-center text-sm text-dslGray-4`};
    }
  }

  .react-datepicker__day {
    ${tw`w-10 h-10 flex items-center justify-center m-0 text-sm hover:(bg-dslGray-1)`};

    &--outside-month {
      ${tw`text-dslGray-3`};
    }

    &--in-range {
      ${tw`bg-transparent text-dslBlack-2`}
    }

    &--selected {
      ${tw`
        bg-dslBlue-dark
        text-dslWhite
        font-bold
        hover:(bg-dslBlue-dark)
      `};
    }

    &--disabled {
      ${tw`
        pointer-events-none
        text-dslGray-3
      `};
    }
  }

  .react-datepicker__week:has(.react-datepicker__day--in-range)
    .react-datepicker__day--in-range {
    ${tw`hover:bg-dslBlue-light`}
  }

  .react-datepicker__week:has(.react-datepicker__day--in-range)
    .react-datepicker__day--selected {
    ${tw`hover:bg-dslBlue-dark`}
  }

  .react-datepicker-popper {
    ${tw`z-50 mt-2.5 mb-5`}
  }
`;

const DefaultInput = forwardRef(
  ({ value, onClick, isOpen, form, disabled, placeholder }, ref) => {
    const date = value ? DateTime.fromFormat(value, 'LL/dd/yyyy') : null;
    const displayLabel = date?.isValid
      ? date.toFormat('LLLL d, yyyy')
      : placeholder;

    return (
      <>
        <input
          ref={ref}
          readOnly
          type="hidden"
          value={date?.isValid ? date.toJSDate() : ''}
          name="date"
          form={form}
        />
        <DSButton
          className="date-outer-button"
          label={displayLabel}
          onClick={onClick}
          variant="outline"
          selected={isOpen}
          icon={<Calendar />}
          disabled={disabled}
        />
      </>
    );
  },
);

DefaultInput.displayName = 'CustomInput';
DefaultInput.propTypes = {
  value: PropTypes.string, // DatePicker pass-through
  onClick: PropTypes.func, // DatePicker pass-through
  ref: PropTypes.object, // DatePicker pass-through
  form: PropTypes.string, // DatePicker pass-through
  isOpen: PropTypes.bool,
  disabled: PropTypes.bool,
  placeholder: PropTypes.string,
};

const StyledCustomHeader = styled.div`
  ${tw`
    flex flex-col
  `};

  .ds-datepicker-header__month-controls {
    ${tw`
      flex items-center w-full justify-between
    `};

    .ds-datepicker-header__month-controls__label {
      ${tw`
        text-sm md:text-base font-bold text-start
      `};
    }

    .header-controls-right {
      ${tw`flex items-center gap-3.5`}
    }
  }
`;

const StyledDayContents = styled.div`
  ${tw`
    flex
    flex-col
    items-center
    text-xs
  `}

  .day-dot {
    ${tw`
      w-[5px]
      h-[5px]
      rounded-full
    `}

    ${({ selected, fadedDot }) =>
      fadedDot
        ? tw`bg-dslGray-3`
        : selected
          ? tw`bg-dslBlue-light`
          : tw`bg-dslBlue-dark`}
  }
`;

const StyledChevronButton = styled.button`
  ${({ disabled }) => (disabled ? tw`opacity-30` : tw`opacity-100`)}
`;

const DSDatePicker = ({
  customInput: CustomInput = DefaultInput,
  selectedDate,
  onChange,
  formId,
  startDate,
  endDate,
  popperPlacement,
  popperModifiers,
  minDate,
  maxDate,
  disabled,
  placeholder,
  selectedMonth,
  setSelectedMonth,
  monthSessions,
  autoWidth,
  type = 'day',
}) => {
  const [isOpen, setIsOpen] = useState(false);

  const CustomHeader = ({
    date,
    changeYear,
    changeMonth,
    decreaseMonth,
    increaseMonth,
    prevMonthButtonDisabled,
    nextMonthButtonDisabled,
  }) => {
    const handleTodayClicked = () => {
      const today = DateTime.local();
      changeMonth(today.month - 1); // month is 0-indexed in DatePicker but 1-indexed in DateTime
      changeYear(today.year);
      onChange(today.toJSDate());
    };

    const handleIncreaseMonth = () => {
      increaseMonth();
      if (setSelectedMonth) {
        setSelectedMonth(DateTime.fromJSDate(date).plus({ months: 1 }));
      }
    };

    const handleDecreaseMonth = () => {
      decreaseMonth();
      if (setSelectedMonth) {
        setSelectedMonth(DateTime.fromJSDate(date).minus({ months: 1 }));
      }
    };

    return (
      <StyledCustomHeader>
        <div className="ds-datepicker-header__month-controls">
          <div className="ds-datepicker-header__month-controls__label">
            {type === 'day'
              ? DateTime.fromJSDate(date).toFormat('LLLL yyyy').toUpperCase()
              : DateTime.fromJSDate(date).toFormat('yyyy').toUpperCase()}
          </div>
          <div className="header-controls-right">
            <DSButtonSmall
              label="Today"
              onClick={handleTodayClicked}
              outline
              hug
            />
            <StyledChevronButton
              onClick={handleDecreaseMonth}
              disabled={prevMonthButtonDisabled}
            >
              <ChevronLeft />
            </StyledChevronButton>
            <StyledChevronButton
              onClick={handleIncreaseMonth}
              disabled={nextMonthButtonDisabled}
            >
              <ChevronLeft rotate="right" />
            </StyledChevronButton>
          </div>
        </div>
      </StyledCustomHeader>
    );
  };

  CustomHeader.propTypes = {
    date: PropTypes.string,
    changeYear: PropTypes.func,
    changeMonth: PropTypes.func,
    decreaseMonth: PropTypes.func,
    increaseMonth: PropTypes.func,
    prevMonthButtonDisabled: PropTypes.bool,
    nextMonthButtonDisabled: PropTypes.bool,
    disabled: PropTypes.bool,
    type: PropTypes.oneOf(['day', 'month', 'year']),
  };

  const renderDayContents = (day, date, selectedDate) => {
    const dateMonth = DateTime.fromJSDate(date).get('month');
    const getISODate = (date) => DateTime.fromJSDate(date).toISODate();

    const formattedDate = DateTime.fromJSDate(date).toFormat('d');
    const selected = getISODate(date) === getISODate(selectedDate);
    const outOfRange =
      getISODate(minDate) > getISODate(date) > getISODate(maxDate);
    const event = monthSessions
      ? monthSessions.find(
          (session) =>
            DateTime.fromISO(session.start_timestamp).toISODate() ===
            getISODate(date),
        )
      : null;

    return (
      <StyledDayContents
        selected={selected}
        fadedDot={dateMonth !== selectedMonth?.month}
      >
        <div>{formattedDate}</div>
        {!outOfRange && event && <div className="day-dot" />}
      </StyledDayContents>
    );
  };

  const getPickerPropsByType = (pickerType) => {
    switch (pickerType) {
      case 'month':
        return { showMonthYearPicker: true, dateFormat: 'MMMM yyyy' };
      case 'year':
        return { showYearPicker: true, dateFormat: 'yyyy' };
      case 'day':
      default:
        return { dateFormat: 'MM/dd/yyyy' };
    }
  };

  const pickerProps = getPickerPropsByType(type);

  return (
    <StyledDatePickerContainer autoWidth={autoWidth} type={type}>
      <DatePicker
        customInput={
          <CustomInput
            isOpen={isOpen}
            disabled={disabled}
            placeholder={placeholder}
            type={type}
          />
        }
        selected={selectedDate}
        startDate={startDate}
        endDate={endDate}
        onChange={onChange}
        startOpen={isOpen}
        onCalendarOpen={() => setIsOpen(true)}
        onCalendarClose={() => setIsOpen(false)}
        renderCustomHeader={(props) => <CustomHeader {...props} type={type} />}
        calendarStartDay={1}
        form={formId}
        minDate={minDate}
        maxDate={maxDate}
        popperPlacement={popperPlacement}
        popperModifiers={popperModifiers}
        disabled={disabled}
        placeholderText={placeholder}
        renderDayContents={(day, date) =>
          renderDayContents(day, date, selectedDate)
        }
        prevMonthButtonDisabled={
          selectedDate &&
          minDate &&
          DateTime.fromJSDate(selectedDate).startOf('month') <=
            DateTime.fromJSDate(minDate).startOf('month')
        }
        nextMonthButtonDisabled={
          selectedDate &&
          maxDate &&
          DateTime.fromJSDate(selectedDate).startOf('month') >=
            DateTime.fromJSDate(maxDate).startOf('month')
        }
        {...pickerProps}
      />
    </StyledDatePickerContainer>
  );
};

DSDatePicker.propTypes = {
  customInput: PropTypes.elementType,
  selectedDate: PropTypes.instanceOf(Date),
  startDate: PropTypes.instanceOf(Date),
  endDate: PropTypes.instanceOf(Date),
  minDate: PropTypes.instanceOf(Date),
  maxDate: PropTypes.instanceOf(Date),
  onChange: PropTypes.func,
  formId: PropTypes.string,
  popperPlacement: PropTypes.string,
  popperModifiers: PropTypes.array,
  disabled: PropTypes.bool,
  placeholder: PropTypes.string,
  selectedMonth: PropTypes.instanceOf(DateTime),
  setSelectedMonth: PropTypes.func,
  monthSessions: PropTypes.array,
  autoWidth: PropTypes.bool,
  type: PropTypes.oneOf(['day', 'month', 'year']),
};

export default DSDatePicker;
