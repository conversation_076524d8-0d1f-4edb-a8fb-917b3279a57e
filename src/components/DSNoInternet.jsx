import { useContext } from 'react';
import { NetworkStatusContext } from '../hooks/NetworkStatusContext';
import tw, { styled } from 'twin.macro';

const StyledContainer = styled.div`
  ${tw`fixed top-0 left-0 right-0 bottom-0  flex items-center justify-center z-[9999]`}
`;

const StyledPageContainer = styled.div`
  ${tw`
    flex flex-col items-center
    h-auto
    bg-dslBlack-2
    text-dslWhite
    p-10
    min-h-full
    bg-transparent
  `};
  .bg-video-container {
    ${tw`
      fixed
      top-0 left-0 right-0 bottom-0
      -z-10
    `};
    .bg-dark-overlay {
      ${tw`
        w-screen h-screen
        absolute
        top-0 left-0
      `};
      background: linear-gradient(
        50deg,
        rgba(0, 0, 0, 0.52) 0%,
        rgba(0, 0, 0, 0.2) 100.6%
      );
    }
    .bg-video {
      ${tw`
        w-screen h-screen
        object-cover
      `};
    }
  }
`;

const StyledMessage = styled.p`
  ${tw`text-white text-2xl font-bold`}
`;

// Usage in your component
const DSNoInternet = () => {
  const { isOnline } = useContext(NetworkStatusContext);

  if (isOnline) return null; // Don't display when online

  return (
    <StyledContainer>
      <StyledPageContainer>
        <div className="bg-video-container">
          <div className="bg-dark-overlay" />
        </div>
        <div tw="flex flex-col justify-center items-center h-screen">
          <StyledMessage>
            No internet connection. Please check your network.
          </StyledMessage>
        </div>
      </StyledPageContainer>
    </StyledContainer>
  );
};

export default DSNoInternet;
