import { useState, useRef } from 'react';
import PropTypes from 'prop-types';
import styled from 'styled-components';
import tw from 'twin.macro';

import { ChevronDown, Lock } from '../components/svg/icons';

import { useOutsideClicked } from '../hooks/index.mjs';

export const DROPDOWN_VARIANTS = {
  dark: 'dark',
  light: 'light',
};

const StyledSelectDropdown = styled.div`
  ${tw`
    w-full
    relative
    inline-flex
    flex-nowrap
  `}

  ${(props) => (props.hidden ? tw`hidden` : tw``)}
`;

const StyledCommonDropdownButton = styled.button`
  ${tw`
    flex
    items-center
    justify-between
    border
    border-solid
    rounded
    w-full
    transition-all
    duration-300
    ease-in-out
    hover:(
      border-dslBlue-normal
    )
    disabled:(
      border-dslGray-2 
      bg-dslGray-2
      pointer-events-none
    )
  `}

  ${(props) => (props.open ? tw`border-dslBlue-normal` : tw``)}
  ${(props) => (props.locked ? tw`pointer-events-none cursor-default` : tw``)}
  ${(props) =>
    props.condensed
      ? tw`px-[1px] py-0 h-auto`
      : tw`px-[15px] py-[14px] h-10 md:h-12`}

  .dropdown-btn-left {
    ${tw`
      flex
      items-center
    `}

    .dropdown-btn-selected-text {
      ${(props) => (props.disabled || props.locked ? tw`text-dslGray-4` : tw``)}
      ${(props) => (props.condensed ? tw`text-xs` : tw`text-[14px]`)}
      
      ${({ boldSelection }) =>
        boldSelection ? tw`font-bold text-base uppercase` : tw``}
    }
  }

  svg: &last-child {
    ${tw`
      transition
      ease-in-out
      duration-300
    `}
    ${(props) => (props.open ? tw`scale-y-[-1]` : tw``)}
  }
`;

const StyledLightDropdownButton = styled(StyledCommonDropdownButton)`
  ${tw`
    border-dslGray-1
    bg-dslWhite
  `}

  .dropdown-btn-selected-text {
    ${tw`text-dslBlack-2`}
  }
`;

const StyledDarkDropdownButton = styled(StyledCommonDropdownButton)`
  ${tw`
    border-transparent
    bg-dslBlack-faded-50
    backdrop-blur-lg
  `}

  .dropdown-btn-selected-text {
    ${tw`text-dslWhite`}
  }
`;

const StyledDropdownItems = styled.div`
  ${tw`
    absolute
    top-[54px]
    w-full
    rounded-[3px]
    shadow-dslMedium
    bg-dslWhite
    border
    border-solid
    border-dslGray-2
    px-[4px]
    overflow-y-auto
    max-h-[266px]
    z-10
  `}

  ${(props) => (props.condensed ? tw`top-8` : tw`top-12`)}
`;

const StyledDropdownItem = styled.button`
  ${tw`
    flex
    items-center
    w-full
    p-2
    mt-[4px]
    text-[14px]
    rounded-[3px]

    hover:(
      bg-dslGray-2
    )

    active:(
      bg-dslBlue-light
    )

    last:(
      mb-[4px]
    )

    disabled:(
      opacity-40
      pointer-events-none
    )
  `}

  ${(props) =>
    props.selected
      ? tw`bg-dslBlue-dark hover:bg-dslBlue-dark`
      : tw`bg-dslWhite`}

  .dropdown-item-text {
    ${tw`text-[14px]`}
    ${(props) => (props.selected ? tw`text-dslWhite` : tw`text-dslBlack-1`)}
  }
`;

const StyledDropdownIcon = styled.div`
  ${tw`
    flex
    items-center
  `}

  ${(props) => (props.isItem ? tw`mr-[10px]` : tw`mr-[5px]`)}
  ${(props) => (props.condensed ? tw`w-3.5 h-3.5` : tw`w-6 h-6`)}

  svg {
    path {
      ${(props) =>
        props.condensed
          ? tw``
          : props.selected || props.variant === DROPDOWN_VARIANTS.dark
            ? tw`fill-dslWhite`
            : tw`fill-dslGray-4
      `}
    }
  }
`;

const DROPDOWN_BUTTON_VARIANTS = {
  dark: StyledDarkDropdownButton,
  light: StyledLightDropdownButton,
};

const DSSelectDropdown = ({
  placeholder,
  options,
  selectedOption,
  variant,
  iconElement,
  onChangeSelection,
  disabled,
  hidden,
  locked,
  boldSelection,
  condensed,
}) => {
  const [open, setOpen] = useState(false);
  const Icon = iconElement;
  const dropdownRef = useRef(null);

  const StyledDropdownButton =
    DROPDOWN_BUTTON_VARIANTS[variant] ?? DROPDOWN_BUTTON_VARIANTS.light;

  const handleChangeSelection = (option) => {
    setOpen(false);
    onChangeSelection(option);
  };

  const handleToggleDropdown = () => {
    if (locked) {
      return;
    }
    setOpen(!open);
  };

  useOutsideClicked(dropdownRef, () => setOpen(false));

  return (
    <StyledSelectDropdown ref={dropdownRef} hidden={hidden}>
      <StyledDropdownButton
        onClick={handleToggleDropdown}
        open={open}
        disabled={disabled}
        locked={locked}
        boldSelection={boldSelection}
        condensed={condensed}
      >
        <div className="dropdown-btn-left">
          {iconElement && (
            <StyledDropdownIcon
              variant={variant}
              locked={locked}
              condensed={condensed}
            >
              <Icon />
            </StyledDropdownIcon>
          )}
          <div className="dropdown-btn-selected-text">
            {selectedOption ?? placeholder}
          </div>
        </div>
        {locked ? (
          <Lock color={'#A6A6A6'} />
        ) : (
          <ChevronDown
            color={variant === DROPDOWN_VARIANTS.dark ? '#fff' : '#6B6B6B'}
          />
        )}
      </StyledDropdownButton>
      {open && (
        <StyledDropdownItems condensed={condensed}>
          {options.map((option) => {
            const ItemIcon = option.icon;
            return (
              <StyledDropdownItem
                id="ds-dropdown-item"
                key={option?.id}
                className="dropdown-item"
                selected={selectedOption === option?.label}
                onClick={() => handleChangeSelection(option)}
                disabled={option?.disabled}
              >
                {ItemIcon && (
                  <StyledDropdownIcon
                    selected={selectedOption === option?.label}
                    isItem
                  >
                    <ItemIcon />
                  </StyledDropdownIcon>
                )}
                <div className="dropdown-item-text">{option?.label}</div>
              </StyledDropdownItem>
            );
          })}
        </StyledDropdownItems>
      )}
    </StyledSelectDropdown>
  );
};

DSSelectDropdown.displayName = 'DSSelectDropdown';

DSSelectDropdown.propTypes = {
  options: PropTypes.arrayOf(
    PropTypes.shape({
      id: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
      label: PropTypes.string,
      icon: PropTypes.elementType,
    }),
  ).isRequired,
  iconElement: PropTypes.elementType,
  placeholder: PropTypes.string.isRequired,
  selectedOption: PropTypes.string,
  variant: PropTypes.oneOf(Object.keys(DROPDOWN_VARIANTS)),
  disabled: PropTypes.bool,
  onChangeSelection: PropTypes.func,
  hidden: PropTypes.bool,
  locked: PropTypes.bool,
  boldSelection: PropTypes.bool,
  condensed: PropTypes.bool,
};

export default DSSelectDropdown;
