import { useState, useEffect } from 'react';
import { theme } from 'twin.macro';
import { DateTime } from 'luxon';
import PropTypes from 'prop-types';

// import { Clock } from '../components/svg/icons';

const DSPodLocalClock = ({ timezone }) => {
  const [podTime, setPodTime] = useState('');

  useEffect(() => {
    const setCurrentPodTime = () => {
      const now = DateTime.now().setZone(timezone).toFormat('h:mma');
      setPodTime(now);
    };

    const interval = setInterval(() => {
      setCurrentPodTime();
    }, 1000);

    // Initialize the pod time
    setCurrentPodTime();

    return () => clearInterval(interval);
  }, [timezone]);

  return (
    <div tw="flex flex-row gap-2 text-dslGray-4">
      {/* <Clock color={theme`colors.dslGray.4`} /> */}
      <div>
        <span tw="font-bold">{podTime}</span> site group time
      </div>
    </div>
  );
};

DSPodLocalClock.propTypes = {
  timezone: PropTypes.string,
};

export default DSPodLocalClock;
