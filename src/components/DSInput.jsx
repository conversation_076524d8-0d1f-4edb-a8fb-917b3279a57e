import tw, { theme } from 'twin.macro';
import styled from 'styled-components';
import PropTypes from 'prop-types';

const StyledDSInputContainer = styled.div`
  ${tw`flex flex-col relative transition-all duration-300 ease-in-out w-full`};

  .ds-input-start-icon {
    ${tw`
      absolute left-0 top-0 bottom-0 z-10
      flex items-center justify-center
      pl-4
    `};
  }

  .ds-input-end-icon,
  .ds-input-suffix {
    ${tw`
      absolute right-0 top-0 bottom-0 z-10
      flex items-center justify-center
      pr-4 pl-3
      cursor-pointer
    `};
  }

  .ds-input-suffix {
    ${tw`
      cursor-default
      text-base
    `}

    ${({ disabled }) => (disabled ? tw`text-dslGray-5` : tw``)}
  }
`;

const StyledDSInput = styled.input`
  ${tw`
    transition-all duration-300 ease-in-out
    w-full
    py-2 md:py-4
    text-sm text-dslBlack-2
    bg-transparent
    border border-dslGray-1 rounded
    appearance-none
    box-border
    outline-none
    placeholder:(transition-all duration-300 ease-in-out text-dslGray-3)
    focus:border-dslBlue-normal
    disabled:(
      border-dslGray-2 
      bg-dslGray-2
      pointer-events-none
      text-dslGray-5
    )
  `};

  ${({ hasStartIcon }) => (hasStartIcon ? tw`pl-10 pr-2` : tw`px-2`)};
  ${({ hasEndIcon }) => (hasEndIcon ? tw`pr-12 pl-2` : undefined)};

  ${({ dark }) =>
    dark
      ? tw`
        text-dslWhite
        border-transparent
        bg-dslBlack-faded-50
        backdrop-blur-lg
      `
      : undefined};

  ${({ error }) =>
    error
      ? tw`placeholder:(transition-all duration-300 ease-in-out)`
      : undefined};
`;

const DSInput = ({
  endIconElement,
  endIconOnClick,
  startIconElement,
  suffix,
  disabled,
  ...props
}) => {
  const StartIcon = startIconElement;
  const EndIcon = endIconElement;
  const startIconColor = props.dark
    ? theme`colors.dslWhite`
    : theme`colors.dslGray.4`;
  const endIconColor = props.dark
    ? theme`colors.dslGray.3`
    : theme`colors.dslBlack.2`;
  return (
    <StyledDSInputContainer
      error={props.error}
      dark={props.dark}
      disabled={disabled}
    >
      {StartIcon ? (
        <div className="ds-input-start-icon">
          <StartIcon color={startIconColor} width={'18'} height={'18'} />
        </div>
      ) : null}
      <StyledDSInput
        hasEndIcon={!!EndIcon}
        hasStartIcon={!!StartIcon}
        disabled={disabled}
        {...props}
      />
      {EndIcon ? (
        <i onClick={endIconOnClick} className="ds-input-end-icon">
          <EndIcon color={endIconColor} />
        </i>
      ) : null}
      {suffix ? <div className="ds-input-suffix">{suffix}</div> : null}
    </StyledDSInputContainer>
  );
};

DSInput.propTypes = {
  /**
   * Label for the input field
   */
  startIconElement: PropTypes.elementType,
  endIconElement: PropTypes.elementType,
  endIconOnClick: PropTypes.func,
  error: PropTypes.bool,
  dark: PropTypes.bool,
  suffix: PropTypes.string,
  disabled: PropTypes.bool,
};

export default DSInput;
