import PropTypes from 'prop-types';
import styled from 'styled-components';
import tw from 'twin.macro';

import {
  DSPopover,
  DSBadge,
  DSInput,
  DSSelectDropdown,
  DSButton,
  DSLoadingSpinner,
  DSTooltip,
  DSButtonSmall,
} from '../components';
import { BADGE_COLORS } from './DSBadge';
import { Check, Trash, Email } from '../components/svg/icons';
import { useRef, useState, useEffect } from 'react';
import { BTN_TYPES } from './DSButton';

const StyledDSUserDetails = styled.div`
  ${tw``}

  .heading-row {
    ${tw`
      flex
      items-center
      justify-between
      px-10
      pt-7.5
      pb-[15px]
      border-b
      border-solid
      border-dslGray-2
    `}

    .header {
      ${tw`
        font-bold
        text-[15px]
        text-dslGray-4
        uppercase
      `}
    }
  }
  .user-details-cont {
    ${tw`
      grid
      grid-cols-2
      gap-6
      px-10
      py-7.5
      w-full
    `}
    .event-reserved-by-section {
      ${tw`
        cursor-pointer
      `};
    }

    .input-header {
      ${tw`
        text-[10px]
        text-dslGray-4
      `}
    }

    .error-text {
      ${tw`
        text-dslStatus-red
        text-xs
        mt-1
      `}
    }
  }
`;

const DSUserDetails = ({
  isSelfAccount,
  showModal,
  onClose,
  user,
  userRole,
  newEmail,
  onChangeUserRole,
  onChangeEmail,
  confirmDeleteUser,
  loading,
  dropdownOptions,
  updateUserRole,
  resendInvitation,
  editDisabled,
  emailError,
}) => {
  const emailRef = useRef(null);
  const firstNameRef = useRef(null);
  const lastNameRef = useRef(null);
  const dropdownRef = useRef(null);

  const [emailHover, setEmailHover] = useState(false);
  const [firstNameHover, setFirstNameHover] = useState(false);
  const [lastNameHover, setLastNameHover] = useState(false);
  const [dropDownHover, setDropDownHover] = useState(false);

  const popoverHeading =
    isSelfAccount || user?.account_created
      ? `${user?.first_name} ${user?.last_name}`
      : user?.user_email;

  const newUser = !user;

  const isSaveDisabled = () => {
    return (
      userRole?.id === user?.mrm_role ||
      (userRole?.id === 'super_admin' && user?.super_admin === true) ||
      (newUser && (!userRole || !newEmail))
    );
  };

  const saveDisabled = isSaveDisabled();

  const popoverContent = (
    <StyledDSUserDetails>
      <div className="heading-row">
        <div className="header">
          {isSelfAccount ? 'MY DETAILS' : 'About the Person'}
        </div>
        {newUser ? null : isSelfAccount || user?.account_created ? (
          <DSBadge color={BADGE_COLORS.OUTLINE_DARK} condensed>
            Enrolled
          </DSBadge>
        ) : (
          <DSBadge color={BADGE_COLORS.GRAY} condensed>
            Pending
          </DSBadge>
        )}
      </div>
      <div className="user-details-cont">
        <div tw="col-span-2">
          <div className="input-header">Email*</div>
          <div className="event-reserved-by-section" ref={emailRef}>
            <DSInput
              placeholder="Email"
              value={newUser ? newEmail : (user?.user_email ?? user?.email)}
              onChange={(e) => onChangeEmail(e.target.value)}
              disabled={!newUser}
              error={!!emailError}
            />
          </div>
          <DSTooltip
            label={
              "These fields are disabled and aren't editable from \nDreamscape Learn Control Center"
            }
            show={emailHover}
            positionStyles={tw`left-[300px] w-[346px]`}
          />
          {emailError && <div className="error-text">{emailError}</div>}
        </div>
        {isSelfAccount || user?.account_created ? (
          <>
            <div>
              <div className="input-header">First Name</div>
              <div className="event-reserved-by-section" ref={firstNameRef}>
                <DSInput
                  placeholder="First Name"
                  value={user?.first_name}
                  disabled
                />
              </div>
              <DSTooltip
                label={
                  "These fields are disabled and aren't editable from \nDreamscape Learn Control Center"
                }
                show={firstNameHover}
                positionStyles={tw`left-[100px] w-[346px]`}
              />
            </div>
            <div>
              <div className="input-header">Last Name</div>
              <div className="event-reserved-by-section" ref={lastNameRef}>
                <DSInput
                  placeholder="Last Name"
                  value={user?.last_name}
                  disabled
                />
              </div>
              <DSTooltip
                label={
                  "These fields are disabled and aren't editable from \nDreamscape Learn Control Center"
                }
                show={lastNameHover}
                positionStyles={tw`right-12 w-[346px]`}
              />
            </div>
          </>
        ) : null}
      </div>
      <div className="heading-row">
        <div className="header">
          {isSelfAccount ? 'MANAGE ACCOUNT' : 'Account Management'}
        </div>
      </div>
      <div className="user-details-cont">
        <div tw="col-span-2">
          <div tw="flex justify-between items-center gap-4" ref={dropdownRef}>
            <div tw="flex-1">
              <DSSelectDropdown
                options={dropdownOptions}
                placeholder="Select role..."
                selectedOption={userRole?.label}
                onChangeSelection={onChangeUserRole}
                disabled={editDisabled}
              />
            </div>

            {isSelfAccount && (
              <div tw="flex-1 flex justify-center">
                <DSButton
                  label="Reset Password"
                  variant={BTN_TYPES.outline}
                  fullWidth
                  disabled={true} // {editDisabled || loading}
                  onClick={resendInvitation}
                />
              </div>
            )}
          </div>
        </div>
      </div>
    </StyledDSUserDetails>
  );

  const headerButtons =
    newUser || isSelfAccount
      ? []
      : [
          <DSButtonSmall
            tw="mr-6"
            key="delete-user"
            icon={Trash}
            onClick={() => confirmDeleteUser(user)}
            disabled={editDisabled || newUser}
          />,
        ];

  const footerButton = !isSelfAccount ? (
    <>
      {(isSelfAccount || user?.account_created || !newUser) && (
        <DSButton
          label={loading ? '' : 'RESEND INVITE'}
          variant={BTN_TYPES.outline}
          hug
          disabled={true} //{editDisabled || loading}
          icon={loading ? <DSLoadingSpinner /> : <Email />}
          onClick={resendInvitation}
        />
      )}

      <DSButton
        label={
          loading
            ? ''
            : newUser
              ? 'Create User'
              : saveDisabled
                ? 'Saved'
                : 'Save'
        }
        disabled={saveDisabled || loading}
        icon={
          loading ? (
            <DSLoadingSpinner />
          ) : saveDisabled && !newUser ? (
            <Check />
          ) : undefined
        }
        onClick={updateUserRole}
      />
    </>
  ) : null;

  return (
    <DSPopover
      showModal={showModal}
      onClose={onClose}
      content={popoverContent}
      subHeading={newUser ? 'New Person' : 'Details'}
      heading={newUser ? 'Details' : popoverHeading}
      userData={{ role: userRole?.id ?? user?.mrm_role }}
      footerButton={footerButton}
      headerButtons={headerButtons}
      scrollable={false}
    />
  );
};

DSUserDetails.propTypes = {
  showModal: PropTypes.bool,
  onClose: PropTypes.func,
  user: PropTypes.shape({
    id: PropTypes.string,
    first_name: PropTypes.string,
    last_name: PropTypes.string,
    email: PropTypes.string,
    account_created: PropTypes.bool,
    role: PropTypes.string,
  }),
  isSelfAccount: PropTypes.bool,
  newEmail: PropTypes.string,
  userRole: PropTypes.object,
  onChangeUserRole: PropTypes.func,
  onChangeEmail: PropTypes.func,
  confirmDeleteUser: PropTypes.func,
  loading: PropTypes.bool,
  dropdownOptions: PropTypes.array,
  updateUserRole: PropTypes.func,
  resendInvitation: PropTypes.func,
  editDisabled: PropTypes.bool,
  emailError: PropTypes.string,
};

export default DSUserDetails;
