import PropTypes from 'prop-types';
import styled from 'styled-components';
import tw from 'twin.macro';

const StyledLoadingSpinner = styled.div`
  ${tw`
    flex
    gap-0.5
    h-[40px]
  `};

  #inContinueButton > div {
    background-color: #e6e6e6;
  }

  & > div {
    ${tw`
      h-full
      w-[6px]
      [animation: sk-stretchdelay 1.2s infinite ease-in-out]
    `};

    ${({ black }) => (black ? tw`bg-dslGray-5` : tw`bg-dslGray-3`)}
  }

  .rect2 {
    ${tw`[animation-delay: -1.1s]`};
  }

  .rect3 {
    ${tw`[animation-delay: -1.0s]`};
  }

  .rect4 {
    ${tw`[animation-delay: -0.9s]`};
  }

  .rect5 {
    ${tw`[animation-delay: -0.8s]`};
  }

  @-webkit-keyframes sk-stretchdelay {
    0%,
    40%,
    100% {
      -webkit-transform: scaleY(0.4);
    }
    20% {
      -webkit-transform: scaleY(1);
    }
  }

  @keyframes sk-stretchdelay {
    0%,
    40%,
    100% {
      transform: scaleY(0.4);
      -webkit-transform: scaleY(0.4);
    }
    20% {
      transform: scaleY(1);
      -webkit-transform: scaleY(1);
    }
  }
`;

const LoadingSpinner = (props) => (
  <StyledLoadingSpinner
    id={props.dark ? undefined : 'inContinueButton'}
    black={props.black}
  >
    <div className="rect1" />
    <div className="rect2" />
    <div className="rect3" />
    <div className="rect4" />
    <div className="rect5" />
  </StyledLoadingSpinner>
);

LoadingSpinner.propTypes = {
  dark: PropTypes.bool,
  black: PropTypes.bool,
};

export default LoadingSpinner;
