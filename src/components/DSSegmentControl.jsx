import PropTypes from 'prop-types';
import styled from 'styled-components';
import tw from 'twin.macro';

const StyledDSSegmentControl = styled.div`
  ${tw`
    inline-flex flex-row
    gap-[1px]
    border border-dslGray-2 rounded
    bg-dslGray-2
    box-border
    overflow-hidden
  `};
`;

const StyledSegementButton = styled.button`
  ${tw`
    transition-all duration-300 ease-in-out
    font-bold
    tracking-widest
    text-dslGray-4
    bg-dslWhite
    uppercase
    p-3.5
  `};

  ${({ active }) =>
    active ? tw`bg-dslBlack-2 text-dslWhite` : tw`hover:bg-dslGray-1`};
`;

const DSSegmentControl = ({ segments, activeSegment, onSegmentSelected }) => {
  const SegmentList = Object.entries(segments).map(([key, value]) => {
    const isActive = key === activeSegment;
    return (
      <StyledSegementButton
        key={key}
        active={isActive}
        onClick={() => onSegmentSelected(key)}
      >
        {value}
      </StyledSegementButton>
    );
  });
  return <StyledDSSegmentControl>{SegmentList}</StyledDSSegmentControl>;
};

DSSegmentControl.propTypes = {
  segments: PropTypes.objectOf(PropTypes.string),
  activeSegment: PropTypes.string,
  onSegmentSelected: PropTypes.func,
};

export default DSSegmentControl;
