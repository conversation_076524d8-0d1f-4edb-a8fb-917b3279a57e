import PropTypes from 'prop-types';
import styled from 'styled-components';
import tw from 'twin.macro';

const StyledIconButton = styled.button`
  ${tw`
    flex flex-row
    items-center justify-center
    h-[38px] w-[38px]
    rounded
    transition-all duration-150 ease-in-out
    hover:(bg-dslBlack-faded-5)
    disabled:pointer-events-none
  `};
`;

const StyledIconContainer = styled.div`
  svg {
    path {
      ${tw`transition-all duration-150 ease-in-out fill-dslGray-4`};
      ${StyledIconButton}:hover & {
        ${tw`fill-dslBlack-2`};
      }
      ${StyledIconButton}:disabled & {
        ${tw`opacity-25`}
      }
    }
  }
`;

const DSIconButton = ({ icon: Icon, ...props }) => {
  return (
    <StyledIconButton {...props}>
      <StyledIconContainer>
        <Icon width="20" height="20" />
      </StyledIconContainer>
    </StyledIconButton>
  );
};

DSIconButton.propTypes = {
  icon: PropTypes.elementType.isRequired,
};

export default DSIconButton;
