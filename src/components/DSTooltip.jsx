import PropTypes from 'prop-types';
import styled from 'styled-components';
import tw from 'twin.macro';

const StyledDSTooltip = styled.div`
  ${tw`
    absolute
    z-30
    bg-dslBlack-2
    px-2.5
    py-1
    border border-dslGray-5
    text-left
    text-xs
    text-dslWhite
    rounded
    transition-all
    duration-300
    ease-in-out
  `}

  ${(props) => (props.show ? tw`visible opacity-100` : tw`invisible opacity-0`)}
  ${(props) => (props.position ? props.position : tw``)}
`;

const DSTooltip = ({ label, show, positionStyles }) => {
  return (
    <StyledDSTooltip show={show} position={positionStyles}>
      {label}
    </StyledDSTooltip>
  );
};

DSTooltip.propTypes = {
  label: PropTypes.string,
  show: PropTypes.bool,
  positionStyles: PropTypes.object,
};

export default DSTooltip;
