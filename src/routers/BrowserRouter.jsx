import { createBrowserRouter, redirect, useRouteError } from 'react-router-dom';

import AuthLayout, { authLoader } from '../routes/AuthLayout';
import SignInRoute, { signInAction, signInLoader } from '../routes/SignIn';
import CreateAccountRoute, {
  createAccountAction,
  createAccountLoader,
} from '../routes/CreateAccount';
import ForgotPasswordRoute, {
  forgotPasswordAction,
  forgotPasswordLoader,
} from '../routes/ForgotPassword';
import ResetPasswordRoute, {
  resetPasswordAction,
  resetPasswordLoader,
} from '../routes/ResetPassword';
import VerifyAccountRoute, {
  verifyAccountAction,
  verifyAccountLoader,
} from '../routes/VerifyAccount';
import DashboardLayout, {
  dashboardLayoutAction,
  dashboardLayoutLoader,
} from '../routes/DashboardLayout';
import Dashboard, { dashboardAction } from '../pages/Dashboard';
import PeopleManagementRoute, {
  peopleManagementAction,
  peopleManagementLoader,
} from '../routes/PeopleManagement';

import ErrorPage from '../components/ErrorPage';

const RootBoundary = () => {
  const error = useRouteError();

  if (error?.status) {
    return (
      <ErrorPage
        status={error.status}
        message={error.statusText || error.message}
      />
    );
  }

  return (
    <ErrorPage
      status="Unknown Error"
      message="Something went wrong, please try again later."
    />
  );
};

const router = createBrowserRouter([
  {
    path: '/',
    id: 'auth/root',
    element: <AuthLayout />,
    loader: authLoader,
    children: [
      {
        index: true,
        element: <h1>Root</h1>,
        loader: () => {
          return redirect('/signin');
        },
      },
      {
        path: '/signin',
        id: 'auth/signin',
        element: <SignInRoute />,
        loader: signInLoader,
        action: signInAction,
      },
      {
        path: '/create-account',
        id: 'auth/create-account',
        element: <CreateAccountRoute />,
        loader: createAccountLoader,
        action: createAccountAction,
      },
      {
        path: '/forgot-password',
        id: 'auth/forgot-password',
        element: <ForgotPasswordRoute />,
        loader: forgotPasswordLoader,
        action: forgotPasswordAction,
      },
      {
        path: '/verify-account',
        id: 'auth/verify-account',
        element: <VerifyAccountRoute />,
        loader: verifyAccountLoader,
        action: verifyAccountAction,
      },
      {
        path: '/reset-password',
        id: 'auth/reset-password',
        element: <ResetPasswordRoute />,
        loader: resetPasswordLoader,
        action: resetPasswordAction,
      },
      {
        path: '/test',
        element: (
          <div style={{ padding: '20px', fontSize: '18px', fontFamily: 'Arial, sans-serif' }}>
            <h1 style={{ color: 'green' }}>✅ Test Page - Frontend is Working!</h1>
            <p>If you can see this, the React frontend is loading correctly.</p>
            <p>Server time: {new Date().toLocaleString()}</p>
            <p>Charles Proxy status: Ready for SSL proxying</p>
            <div style={{ marginTop: '20px' }}>
              <button
                onClick={() => {
                  console.log('🔍 Making test API call...');
                  fetch('/api/mrm-access/profile', {
                    headers: { 'Authorization': 'Bearer test-token' }
                  })
                    .then(r => {
                      console.log('✅ Response received:', r.status, r.statusText);
                      return r.json();
                    })
                    .then(data => console.log('📄 API Response:', data))
                    .catch(err => console.log('❌ API Error (expected):', err));
                }}
                style={{
                  padding: '10px 20px',
                  backgroundColor: '#007bff',
                  color: 'white',
                  border: 'none',
                  borderRadius: '5px',
                  cursor: 'pointer',
                  marginRight: '10px'
                }}
              >
                🔍 Test API Call (Check Charles!)
              </button>
              <button
                onClick={() => {
                  console.log('🌐 Making external HTTPS call...');
                  fetch('https://httpbin.org/get')
                    .then(r => {
                      console.log('✅ External response:', r.status);
                      return r.json();
                    })
                    .then(data => console.log('📄 External data:', data))
                    .catch(err => console.log('❌ External error:', err));
                }}
                style={{
                  padding: '10px 20px',
                  backgroundColor: '#28a745',
                  color: 'white',
                  border: 'none',
                  borderRadius: '5px',
                  cursor: 'pointer',
                  marginLeft: '10px'
                }}
              >
                🌐 Test External HTTPS
              </button>
              <button
                onClick={() => {
                  console.log('🚀 Making direct API call...');
                  fetch('https://api.global.dev003.cloud.techscapelearn.com/mrm-access/profile', {
                    headers: {
                      'Authorization': 'Bearer token=fake-jwt-for-testing',
                      'x-api-key': 'LwMRe1QFMfCuRK97GWzw7XAA42FZnS82rdxwfShj'
                    }
                  })
                    .then(r => {
                      console.log('✅ Direct API response:', r.status);
                      return r.json();
                    })
                    .then(data => console.log('📄 Direct API data:', data))
                    .catch(err => console.log('❌ Direct API error:', err));
                }}
                style={{
                  padding: '10px 20px',
                  backgroundColor: '#dc3545',
                  color: 'white',
                  border: 'none',
                  borderRadius: '5px',
                  cursor: 'pointer',
                  marginLeft: '10px'
                }}
              >
                🚀 Test Direct API
              </button>
              <a href="/signin" style={{ color: 'blue', textDecoration: 'underline' }}>Go to Sign In</a>
            </div>
            <div style={{ marginTop: '20px', fontSize: '14px', color: '#666' }}>
              <p><strong>Charles Proxy Testing:</strong></p>
              <p>1. <strong>Blue button</strong>: Tests local API call (localhost:3000/api/...)</p>
              <p>2. <strong>Green button</strong>: Tests external HTTPS call (httpbin.org)</p>
              <p>3. Open browser DevTools Console (F12) to see request logs</p>
              <p>4. Check Charles Proxy for intercepted traffic</p>
              <hr style={{ margin: '10px 0' }} />
              <p><strong>Charles Setup Required:</strong></p>
              <p>• Enable SSL Proxying for: <code>localhost:3000</code> and <code>httpbin.org</code></p>
              <p>• Install Charles certificate if needed</p>
              <p>• Make sure Charles is recording</p>
            </div>
          </div>
        ),
      },
      {
        path: '*',
        element: <h1>Catch All</h1>,
        loader: () => redirect('/signin'),
      },
    ],
  },
  {
    path: '/app',
    element: <DashboardLayout />,
    id: 'app/root',
    loader: dashboardLayoutLoader,
    action: dashboardLayoutAction,
    errorElement: <RootBoundary />,
    children: [
      {
        index: true,
        path: '/app/dashboard',
        element: <Dashboard />,
        action: dashboardAction,
      },
      {
        path: '/app/people-management',
        element: <PeopleManagementRoute />,
        action: peopleManagementAction,
        loader: peopleManagementLoader,
      },
    ],
  },
]);

export default router;
