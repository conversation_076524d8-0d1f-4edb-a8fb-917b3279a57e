import { createBrowserRouter, redirect, useRouteError } from 'react-router-dom';

import AuthLayout, { authLoader } from '../routes/AuthLayout';
import SignInRoute, { signInAction, signInLoader } from '../routes/SignIn';
import CreateAccountRoute, {
  createAccountAction,
  createAccountLoader,
} from '../routes/CreateAccount';
import ForgotPasswordRoute, {
  forgotPasswordAction,
  forgotPasswordLoader,
} from '../routes/ForgotPassword';
import ResetPasswordRoute, {
  resetPasswordAction,
  resetPasswordLoader,
} from '../routes/ResetPassword';
import VerifyAccountRoute, {
  verifyAccountAction,
  verifyAccountLoader,
} from '../routes/VerifyAccount';
import DashboardLayout, {
  dashboardLayoutAction,
  dashboardLayoutLoader,
} from '../routes/DashboardLayout';
import Dashboard, { dashboardAction } from '../pages/Dashboard';
import PeopleManagementRoute, {
  peopleManagementAction,
  peopleManagementLoader,
} from '../routes/PeopleManagement';

import ErrorPage from '../components/ErrorPage';

const RootBoundary = () => {
  const error = useRouteError();

  if (error?.status) {
    return (
      <ErrorPage
        status={error.status}
        message={error.statusText || error.message}
      />
    );
  }

  return (
    <ErrorPage
      status="Unknown Error"
      message="Something went wrong, please try again later."
    />
  );
};

const router = createBrowserRouter([
  {
    path: '/',
    id: 'auth/root',
    element: <AuthLayout />,
    loader: authLoader,
    children: [
      {
        index: true,
        element: <h1>Root</h1>,
        loader: () => {
          return redirect('/signin');
        },
      },
      {
        path: '/signin',
        id: 'auth/signin',
        element: <SignInRoute />,
        loader: signInLoader,
        action: signInAction,
      },
      {
        path: '/create-account',
        id: 'auth/create-account',
        element: <CreateAccountRoute />,
        loader: createAccountLoader,
        action: createAccountAction,
      },
      {
        path: '/forgot-password',
        id: 'auth/forgot-password',
        element: <ForgotPasswordRoute />,
        loader: forgotPasswordLoader,
        action: forgotPasswordAction,
      },
      {
        path: '/verify-account',
        id: 'auth/verify-account',
        element: <VerifyAccountRoute />,
        loader: verifyAccountLoader,
        action: verifyAccountAction,
      },
      {
        path: '/reset-password',
        id: 'auth/reset-password',
        element: <ResetPasswordRoute />,
        loader: resetPasswordLoader,
        action: resetPasswordAction,
      },
      {
        path: '/test',
        element: (
          <div style={{padding: '20px', fontSize: '18px', fontFamily: 'Arial, sans-serif'}}>
            <h1 style={{color: 'green'}}>✅ Test Page - Frontend is Working!</h1>
            <p>If you can see this, the React frontend is loading correctly.</p>
            <p>Server time: {new Date().toLocaleString()}</p>
            <p>Charles Proxy status: Ready for SSL proxying</p>
            <div style={{marginTop: '20px'}}>
              <a href="/signin" style={{color: 'blue', textDecoration: 'underline'}}>Go to Sign In</a>
            </div>
          </div>
        ),
      },
      {
        path: '*',
        element: <h1>Catch All</h1>,
        loader: () => redirect('/signin'),
      },
    ],
  },
  {
    path: '/app',
    element: <DashboardLayout />,
    id: 'app/root',
    loader: dashboardLayoutLoader,
    action: dashboardLayoutAction,
    errorElement: <RootBoundary />,
    children: [
      {
        index: true,
        path: '/app/dashboard',
        element: <Dashboard />,
        action: dashboardAction,
      },
      {
        path: '/app/people-management',
        element: <PeopleManagementRoute />,
        action: peopleManagementAction,
        loader: peopleManagementLoader,
      },
    ],
  },
]);

export default router;
