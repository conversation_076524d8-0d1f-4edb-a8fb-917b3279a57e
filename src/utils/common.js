import { DateTime } from 'luxon';
import CryptoJS from 'crypto-js';

import { USER_ROLES } from './constants';
import { NAV_LABELS } from '../components/navigation/DSNavItem';

const EK = import.meta.env.VITE_EK;

const NAV_INFO = [
  {
    label: NAV_LABELS.DASHBOARD,
    path: 'dashboard',
    isAdmin: true,
  },
  {
    label: NAV_LABELS.PEOPLE_MANAGEMENT,
    path: 'people-management',
    isAdmin: true,
  },
];

const TRUCK_DRIVER_NAV_INFO = [
  {
    label: NAV_LABELS.DASHBOARD,
    path: '',
    isAdmin: false,
  },
];

const NAV_INFO_MAP = {
  [USER_ROLES.SUPER_ADMIN]: NAV_INFO,
  [USER_ROLES.ADMIN]: NAV_INFO,
  [USER_ROLES.DISTRICT_MANAGER]: NAV_INFO,
  [USER_ROLES.TRUCK_DRIVER]: TRUCK_DRIVER_NAV_INFO,
};

export const replaceTimezone = (isoString, timezone) => {
  // Strip the timezone from the ISO string
  const timeString = DateTime.fromISO(isoString).toISO({
    includeOffset: false,
  });

  // Return a new ISO string with the timezone replaced
  return DateTime.fromISO(timeString, { zone: timezone }).toISO();
};

/**
 *
 * @param {string} data
 * @returns
 */
export const encryptData = (data) => {
  const encrypted = CryptoJS.AES.encrypt(data, EK);
  return encrypted.toString();
};

/**
 *
 * @param {string} data
 * @returns
 */
export const decryptData = (data) => {
  const decrypted = CryptoJS.AES.decrypt(data, EK);
  return decrypted.toString(CryptoJS.enc.Utf8);
};

export class SessionAdapter {
  static toFullCalendarEvent(session) {
    const podNumber = Number(session.pod_id.split('-')[1]);
    return {
      id: session.session_id,
      resourceId: session.gearup_id,
      start: session.start_timestamp,
      end: session.end_timestamp,
      title: session.title_name,
      backgroundColor: 'transparent',
      borderColor: 'transparent',
      // Start of extended props
      gearupId: session.gearup_id,
      isDraft: session.is_draft,
      isGroupSession: session.max_seats === 1,
      podNumber,
      maxSeats: session.max_seats,
      capacity: session.capacity,
      seatsAvailable: session.seats_available,
      seatsReserved: session.seats_reserved,
      scheduleDate: session.schedule_date,
      podId: session.pod_id,
      siteId: session.site_id,
      titleId: session.title_id,
      groupType: session.group_type,
      adaReserved: session.ada_reserved,
      showType: session.show_type,
      localTime: session.local_time,
      durationSeconds: session.duration_sec,
      tlc: session.tlc,
    };
  }

  static fromFullCalendarEvent(event) {
    const {
      address,
      available_end_timestamp,
      available_start_timestamp,
      notes,
      site_group_id,
      site_group_location_id,
      location_info,
    } = event.extendedProps;
    return {
      address,
      available_end_timestamp,
      available_start_timestamp,
      notes,
      site_group_id,
      site_group_location_id,
      resourceId: event._def.resourceIds[0],
      session_id: event.id,
      backgroundColor: event.backgroundColor,
      start_timestamp: DateTime.fromJSDate(event.start).toISO(),
      end_timestamp: DateTime.fromJSDate(event.end).toISO(),
      title_name: event?.title,
      location_info,
    };
  }
}

/* SORTING FUNCTIONS */
export const sortFirstName = (rowA, rowB) => {
  const nameA = rowA.first_name;
  const nameB = rowB.first_name;
  return !nameA ? 1 : !nameB ? -1 : nameA > nameB ? 1 : nameA < nameB ? -1 : 0;
};

export const sortLastName = (rowA, rowB) => {
  const nameA = rowA.last_name;
  const nameB = rowB.last_name;
  return !nameA ? 1 : !nameB ? -1 : nameA > nameB ? 1 : nameA < nameB ? -1 : 0;
};

export const reconcileNavItems = ({ currentRole }) => {
  const userRoleNavInfo = NAV_INFO_MAP[currentRole] || [];
  const activeNavItems = [...userRoleNavInfo];

  return activeNavItems;
};

export const obscureEmailAddress = (email) => {
  const splitEmail = email.split('@');
  const emailName = splitEmail[0];
  const obscuredEmailNameStart = emailName.substring(0, 2);
  const obscuredEmailNameEnd = emailName.substring(
    emailName.length - 2,
    emailName.length,
  );
  return `${obscuredEmailNameStart}****${obscuredEmailNameEnd}@${splitEmail[1]}`;
};
