import { Amplify } from 'aws-amplify';

const userPoolId = import.meta.env.VITE_AWS_USER_POOLS_ID;
const userPoolClientId = import.meta.env.VITE_AWS_USER_POOLS_WEB_CLIENT_ID;
const region = import.meta.env.VITE_AWS_COGNITO_REGION;

const awsConfigs = {
  Auth: {
    Cognito: {
      region,
      userPoolId,
      userPoolClientId,
    },
  }
};

export const configureAmplify = () => {
  Amplify.configure(awsConfigs);
};

export default {
  awsConfigs,
  configureAmplify,
};
