import { theme } from 'twin.macro';
import {
  Individual,
  PeopleGroup,
  EyeOn,
  EyeOff,
} from '../components/svg/icons';

export const USER_ROLES = {
  SUPER_ADMIN: 'super_admin',
  ADMIN: 'admin',
  SCHEDULER: 'scheduler',
  DRIVER: 'driver',
};

export const USER_ROLE_DISPLAY = {
  [USER_ROLES.SUPER_ADMIN]: 'Super Admin',
  [USER_ROLES.ADMIN]: 'Admin',
  [USER_ROLES.SCHEDULER]: 'District Scheduler',
  [USER_ROLES.DRIVER]: 'Driver',
};

export const SHOW_TYPES = {
  1600: 'Public',
  1620: 'Private',
};

export const SHOW_TYPE_DISPLAY = {
  Public: 1600,
  Private: 1620,
};

export const showTypeNumToText = (num) => {
  return SHOW_TYPES[num];
};

export const showTypeTextToNum = (text) => {
  return SHOW_TYPE_DISPLAY[text];
};

export const PRISMIC_STATES = {
  IDLE: 'idle',
  LOADING: 'loading',
  LOADED: 'loaded',
  FAILED: 'failed',
};

export const PRISMIC_LOADING_STATES = [
  PRISMIC_STATES.IDLE,
  PRISMIC_STATES.LOADING,
];

export const RESERVATION_TYPES = {
  INDIVIDUAL: 'Individual',
  GROUP: 'Group',
  SCHEDULE_RESERVE: 'Schedule & Reserve',
};

export const groupDropdownItems = [
  { id: '1', label: RESERVATION_TYPES.INDIVIDUAL, icon: Individual },
  { id: '2', label: RESERVATION_TYPES.GROUP, icon: PeopleGroup },
];

export const visibilityDropdownItems = [
  { id: 1600, label: 'Public', icon: EyeOn },
  { id: 1620, label: 'Private', icon: EyeOff },
];

export const LearnerDropdownItems = [
  { id: '1', label: RESERVATION_TYPES.INDIVIDUAL, icon: Individual },
];

export const reservationOptionDropdownItems = [
  { id: '1', label: RESERVATION_TYPES.INDIVIDUAL, icon: Individual },
  { id: '2', label: RESERVATION_TYPES.GROUP, icon: PeopleGroup },
  { id: '3', label: RESERVATION_TYPES.SCHEDULE_RESERVE, icon: PeopleGroup },
];

export const dayOfWeekMap = {
  Monday: 1,
  Tuesday: 2,
  Wednesday: 3,
  Thursday: 4,
  Friday: 5,
  Saturday: 6,
  Sunday: 7,
};

export const timeRegex = /^([0-1]?[0-9]|2[0-3]):([0-5][0-9])[ ]?([AaPp][Mm])$/;

export const weekdaysOrder = [
  'Monday',
  'Tuesday',
  'Wednesday',
  'Thursday',
  'Friday',
];
export const weekendsOrder = ['Saturday', 'Sunday'];

export const SCHEDULES_COLORS = [
  {
    background: theme`colors.dslBlue.dark`,
    border: theme`colors.dslBlue.light`,
  },
  {
    background: theme`colors.dslStatus.red`,
    border: theme`colors.dslStatus.redLight`,
  },
  {
    background: theme`colors.dslStatus.green`,
    border: theme`colors.dslStatus.greenLight`,
  },
  {
    background: theme`colors.dslStatus.yellow`,
    border: theme`colors.dslStatus.yellowLight`,
  },
  {
    background: theme`colors.dslStatus.purple`,
    border: theme`colors.dslStatus.purpleLight`,
  },
  {
    background: theme`colors.dslStatus.orange`,
    border: theme`colors.dslStatus.orangeLight`,
  },
  {
    background: theme`colors.dslBlack.2`,
    border: theme`colors.dslGray.3`,
  },
  {
    background: theme`colors.dslGray.5`,
    border: theme`colors.dslGray.3`,
  },
];
