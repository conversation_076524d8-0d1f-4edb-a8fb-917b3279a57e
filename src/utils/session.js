import Cookies from 'js-cookie';
import { redirect } from 'react-router-dom';
import {
  signIn,
  signOut,
  signUp,
  autoSignIn,
  fetchAuthSession,
  confirmSignUp,
  resetPassword,
  confirmResetPassword,
  resendSignUpCode,
} from 'aws-amplify/auth';

import { configureAmplify } from './aws_config';
configureAmplify();

export const SESSION_KEY = '__dsl_session';
export const ID_TOKEN_KEY = '__dsl_session_id';
export const TOKEN_CONTEXT_KEY = '__dsl_token_context';

export const USER_ROLE = {
  admin: 'ADMIN',
  operator: 'OPERAT<PERSON>',
  instructor: 'INSTRUCTOR',
  learner: 'LEARNER',
};

export const getCognitoAuthSession = async () => {
  const { tokens } = await fetchAuthSession();
  const token = tokens.accessToken.toString();
  const idToken = tokens.idToken.toString();
  return { token, idToken };
};

export const handleSignUpStep = (nextStep, email) => {
  const { signUpStep } = nextStep;
  switch (signUpStep) {
    case 'CONFIRM_SIGN_UP':
      return redirect(
        `/verify-account?email=${encodeURIComponent(email)}&from=signup`,
      );
    case 'COMPLETE_AUTO_SIGN_IN':
    case 'DONE':
      return autoLogin();
    default:
      return null;
  }
};

export const handleSignInStep = (nextStep, email) => {
  const { signInStep } = nextStep;
  switch (signInStep) {
    case 'CONFIRM_SIGN_UP':
      resendSignUpCode({ username: email });
      return redirect(
        `/verify-account?email=${encodeURIComponent(email)}&from=signin`,
      );
    case 'DONE':
    default:
      return null;
  }
};

export const handleResetPasswordStep = (nextStep, email) => {
  const { resetPasswordStep } = nextStep;
  switch (resetPasswordStep) {
    case 'CONFIRM_RESET_PASSWORD_WITH_CODE':
      return redirect(`/reset-password?email=${encodeURIComponent(email)}`);
    case 'DONE':
      return redirect('/signin');
    default:
      console.error('Unhandled reset password step: ', resetPasswordStep);
      return null;
  }
};

export const login = async (email, password) => {
  if (!email || !password) {
    return null;
  }
  const { nextStep } = await signIn({ username: email, password });
  const stepResult = handleSignInStep(nextStep, email);
  if (stepResult) {
    return stepResult;
  }

  const isDone = !stepResult;
  if (isDone) {
    return getCognitoAuthSession();
  }

  return null;
};

export const logout = async () => {
  await signOut();
  return destroyUserSession();
};

/**
 *
 * @param {object} data
 * @param {string} data.email
 * @param {string} data.password
 * @param {string} data.firstName
 * @param {string} data.lastName
 * @returns
 */
export const register = async ({ email, password, firstName, lastName }) => {
  if (!email || !password || !firstName || !lastName) {
    return null;
  }

  return signUp({
    username: email,
    password,
    options: {
      userAttributes: {
        email,
        given_name: firstName,
        family_name: lastName,
      },
      autoSignIn: {
        enabled: true,
      },
    },
  });
};

export const autoLogin = async () => {
  const { nextStep } = await autoSignIn();
  const isDone = !handleSignInStep(nextStep);
  if (isDone) {
    const { token, idToken } = await getCognitoAuthSession();
    setSession(token, idToken);
    return { token, idToken };
  }
  return null;
};

/**
 *
 * @param {object} data
 * @param {string} data.email
 * @param {string} data.code
 */
export const confirmAccount = async ({ email, code }) => {
  if (!email || !code) {
    return null;
  }

  return confirmSignUp({
    username: email,
    confirmationCode: code,
  });
};

export const resetPasswordRequest = async ({ email }) => {
  if (!email) {
    return null;
  }

  const output = await resetPassword({
    username: email,
  });

  return handleResetPasswordStep(output.nextStep, email);
};

export const confirmResetPasswordRequest = async ({
  email,
  code,
  password,
}) => {
  if (!email || !code || !password) {
    throw new Error('Missing required fields');
  }

  await confirmResetPassword({
    username: email,
    newPassword: password,
    confirmationCode: code,
  });

  // The method confirmResetPassword does not return a nextStep object if successful.
  // The method will only throw an error if unsuccessful. If output is undefined, assume
  // success and return a custom nextStep object.
  const nextStep = { resetPasswordStep: 'DONE' };
  return handleResetPasswordStep(nextStep, email);
};

export const refreshToken = async () => {
  const { tokens } = await fetchAuthSession();
  if (!tokens) {
    // If Cognito returns no tokens from the above call,
    // the user is not logged in. Remove the session.
    removeSession();
    return;
  }
  const token = tokens.accessToken?.toString();
  const idToken = tokens.idToken?.toString();
  setSession(token, idToken);
  return;
};

export const setSession = (token, idToken, tokenContext) => {
  if (!token) {
    removeCookie(SESSION_KEY);
  } else {
    setCookie(SESSION_KEY, token);
  }

  if (!idToken) {
    removeCookie(ID_TOKEN_KEY);
  } else {
    setCookie(ID_TOKEN_KEY, idToken);
  }

  if (!tokenContext) {
    removeCookie(TOKEN_CONTEXT_KEY);
  } else {
    setCookie(TOKEN_CONTEXT_KEY, tokenContext);
  }

  return;
};

export const getSession = () => {
  return {
    token: getCookie(SESSION_KEY),
    idToken: getCookie(ID_TOKEN_KEY),
    lmsTokenContext: getCookie(TOKEN_CONTEXT_KEY),
  };
};

export const removeSession = () => {
  removeCookie(SESSION_KEY);
  removeCookie(ID_TOKEN_KEY);
  removeCookie(TOKEN_CONTEXT_KEY);
  return;
};

export const getCookie = (key) => Cookies.get(key);
export const setCookie = (key, value) => Cookies.set(key, value);
export const removeCookie = (key) => Cookies.remove(key);

export const getSessionItem = (key) => sessionStorage.getItem(key);
export const setSessionItem = (key, value) =>
  sessionStorage.setItem(key, value);
export const removeSessionItem = (key) => sessionStorage.removeItem(key);

export const requireAuth = async (redirectTo = window.pathname) => {
  // Charles Proxy bypass mode - skip authentication for development
  const BYPASS_AUTH = import.meta.env.VITE_BYPASS_AUTH === 'true';

  if (BYPASS_AUTH) {
    console.log('🔧 Authentication bypassed for Charles Proxy development');
    return {
      token: 'fake-access-token-for-charles-proxy',
      idToken: 'Bearer fake-jwt-token-for-charles-proxy-testing',
    };
  }

  // Handle valid users that come in through cognito:
  await refreshToken();
  const { token, idToken } = getSession();

  // Cognito Handling
  if (!token || !idToken) {
    const searchParams = new URLSearchParams([['redirectTo', redirectTo]]);
    throw redirect(`/signin?${searchParams.toString()}`);
  }
  return { token, idToken };
};

export const createUserSession = (token, idToken, tokenContext, redirectTo) => {
  setSession(token, idToken, tokenContext);
  return redirect(redirectTo);
};

export const destroyUserSession = () => {
  removeSession();
  return redirect('/signin');
};
