import axios from 'axios';
import { getSession } from './session';

const genAuthHeaderContent = () => {
  const { idToken } = getSession();
  if (!idToken) {
    return;
  }

  return `Bearer ${idToken}`;
};

const mrmApi = axios.create({
  baseURL: '/api',
  responseType: 'json',
});

// Intercept responses
mrmApi.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    const errorResponse = error.response || {};

    return Promise.reject({
      status: errorResponse.status,
      message: errorResponse.data?.message || 'An error occurred',
    });
  },
);

/* MRM ACCESS API */

const getAuthenticatedUserInfo = async () => {
  const response = await mrmApi.get(`mrm-access/profile`, {
    headers: {
      Authorization: genAuthHeaderContent(),
    },
  });
  return response.data;
};

const getUserlist = async (site_id) => {
  const response = await mrmApi.get(`/mrm-access/site/${site_id}/users`, {
    headers: {
      Authorization: genAuthHeaderContent(),
    },
  });
  return response.data;
};

const addNewUser = async (data, site_id) => {
  const reqData = {
    user_email: data.user_email,
    mrm_role: data.mrm_role,
  };
  const response = await mrmApi.put(
    `/mrm-access/site/${site_id}/user`,
    reqData,
    {
      headers: {
        Authorization: genAuthHeaderContent(),
      },
    },
  );
  return response.data;
};

const updateUserRole = async (user_site_roles_id, role, site_id) => {
  const reqData = {
    mrm_role: role,
  };
  const response = await mrmApi.patch(
    `/mrm-access/site/${site_id}/user/${user_site_roles_id}`,
    reqData,
    {
      headers: {
        Authorization: genAuthHeaderContent(),
      },
    },
  );
  return response.data;
};

const deleteUserRole = async (user_site_roles_id, site_id) => {
  const response = await mrmApi.delete(
    `/mrm-access/site/${site_id}/user/${user_site_roles_id}`,
    {
      headers: {
        Authorization: genAuthHeaderContent(),
      },
    },
  );
  return response.data;
};

const getSuperAdminUserlist = async () => {
  const response = await mrmApi.get(`/mrm-access/users/super-admin`, {
    headers: {
      Authorization: genAuthHeaderContent(),
    },
  });
  return response.data;
};

const addUpdateSuperAdmin = async (data) => {
  const reqData = {
    user_email: data.user_email,
  };
  const response = await mrmApi.put(`/mrm-access/user/super-admin`, reqData, {
    headers: {
      Authorization: genAuthHeaderContent(),
    },
  });
  return response.data;
};

const resendInvitation = async (site_id, email) => {
  const reqData = {
    email: email,
  };
  const response = await mrmApi.post(
    `/mrm-access/${site_id}/invite/resend`,
    reqData,
    {
      headers: {
        Authorization: genAuthHeaderContent(),
      },
    },
  );
  return response.data;
};

const deleteSuperAdmin = async (user_site_roles_id) => {
  const response = await mrmApi.delete(
    `/mrm-access/user/super-admin/${user_site_roles_id}`,
    {
      headers: {
        Authorization: genAuthHeaderContent(),
      },
    },
  );
  return response.data;
};

/* MRM API */

const getSiteGroupInfo = async (site_id) => {
  const response = await mrmApi.get(`/mrm/site/${site_id}/info`, {
    headers: {
      Authorization: genAuthHeaderContent(),
    },
  });
  return response.data;
};

const getMobileSites = async (siteId) => {
  const response = await mrmApi.get(`mrm/site/${siteId}/mobile-sites`, {
    headers: {
      Authorization: genAuthHeaderContent(),
    },
  });
  return response.data;
};

const getSiteGroupLocations = async (siteId) => {
  const response = await mrmApi.get(`mrm/site/${siteId}/locations`, {
    headers: {
      Authorization: genAuthHeaderContent(),
    },
  });
  return response.data;
};

const getMobileSiteSchedules = async (siteId, mobileSiteId) => {
  const response = await mrmApi.get(
    `mrm/site/${siteId}/mobile-site/${mobileSiteId}/schedules`,
    {
      headers: {
        Authorization: genAuthHeaderContent(),
      },
    },
  );
  return response.data;
};

const getMobileSiteBlackouts = async (siteId, mobileSiteId) => {
  const response = await mrmApi.get(
    `mrm/site/${siteId}/mobile-site/${mobileSiteId}/blackouts`,
    {
      headers: {
        Authorization: genAuthHeaderContent(),
      },
    },
  );
  return response.data;
};

const createSchedule = async (siteId, mobileSiteId, reqData) => {
  const response = await mrmApi.put(
    `/mrm/site/${siteId}/mobile-site/${mobileSiteId}/schedule`,
    reqData,
    {
      headers: {
        Authorization: genAuthHeaderContent(),
      },
    },
  );

  return response.data;
};

const removeReservationFromSession = async (
  siteId,
  mobileSiteId,
  locationScheduleId,
) => {
  const response = await mrmApi.delete(
    `/mrm/site/${siteId}/mobile-site/${mobileSiteId}/schedule/${locationScheduleId}`,
    {
      headers: {
        Authorization: genAuthHeaderContent(),
      },
    },
  );
  return response;
};

export default {
  getAuthenticatedUserInfo,
  getUserlist,
  addNewUser,
  updateUserRole,
  deleteUserRole,
  getSuperAdminUserlist,
  addUpdateSuperAdmin,
  resendInvitation,
  deleteSuperAdmin,
  getSiteGroupInfo,
  getMobileSites,
  getSiteGroupLocations,
  getMobileSiteSchedules,
  getMobileSiteBlackouts,
  createSchedule,
  removeReservationFromSession,
};
