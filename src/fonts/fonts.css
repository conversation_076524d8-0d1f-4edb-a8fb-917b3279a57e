/* BROTHER-1816 */
@font-face {
  font-family: 'Brother-1816';
  src: url('./Brother 1816/Brother-1816-Thin.otf') format('opentype');
  font-weight: 100;
}
@font-face {
  font-family: 'Brother-1816';
  src: url('./Brother 1816/Brother-1816-Thin-Italic.otf') format('opentype');
  font-weight: 100;
  font-style: italic;
}
@font-face {
  font-family: 'Brother-1816';
  src: url('./Brother 1816/Brother-1816-Light.otf') format('opentype');
  font-weight: 200;
}
@font-face {
  font-family: 'Brother-1816';
  src: url('./Brother 1816/Brother-1816-Light-Italic.otf') format('opentype');
  font-weight: 200;
  font-style: italic;
}
@font-face {
  font-family: 'Brother-1816';
  src: url('./Brother 1816/Brother-1816-Book.otf') format('opentype');
  font-weight: 300;
}
@font-face {
  font-family: 'Brother-1816';
  src: url('./Brother 1816/Brother-1816-Book-Italic.otf') format('opentype');
  font-weight: 300;
  font-style: italic;
}
@font-face {
  font-family: 'Brother-1816';
  src: url('./Brother 1816/Brother-1816-Regular.otf') format('opentype');
  font-weight: 400;
}
@font-face {
  font-family: 'Brother-1816';
  src: url('./Brother 1816/Brother-1816-Regular-Italic.otf') format('opentype');
  font-weight: 400;
  font-style: italic;
}
@font-face {
  font-family: 'Brother-1816';
  src: url('./Brother 1816/Brother-1816-Medium.otf') format('opentype');
  font-weight: 500;
}
@font-face {
  font-family: 'Brother-1816';
  src: url('./Brother 1816/Brother-1816-Medium-Italic.otf') format('opentype');
  font-weight: 500;
  font-style: italic;
}
@font-face {
  font-family: 'Brother-1816';
  src: url('./Brother 1816/Brother-1816-Bold.otf') format('opentype');
  font-weight: 700;
}
@font-face {
  font-family: 'Brother-1816';
  src: url('./Brother 1816/Brother-1816-Bold-Italic.otf') format('opentype');
  font-weight: 700;
  font-style: italic;
}
@font-face {
  font-family: 'Brother-1816';
  src: url('./Brother 1816/Brother-1816-ExtraBold.otf') format('opentype');
  font-weight: 800;
}
@font-face {
  font-family: 'Brother-1816';
  src: url('./Brother 1816/Brother-1816-ExtraBold-Italic.otf')
    format('opentype');
  font-weight: 800;
  font-style: italic;
}
@font-face {
  font-family: 'Brother-1816';
  src: url('./Brother 1816/Brother-1816-Black.otf') format('opentype');
  font-weight: 900;
}
@font-face {
  font-family: 'Brother-1816';
  src: url('./Brother 1816/Brother-1816-Black-Italic.otf') format('opentype');
  font-weight: 900;
  font-style: italic;
}

/* BELL */
@font-face {
  font-family: 'Bell-MT';
  src: url('./Bell/BellMTStd-Regular.otf') format('opentype');
  font-weight: 400;
}
@font-face {
  font-family: 'Bell-MT';
  src: url('./Bell/BellMTStd-Regular-Italic.otf') format('opentype');
  font-weight: 400;
  font-style: italic;
}
@font-face {
  font-family: 'Bell-MT';
  src: url('./Bell/BellMTStd-SemiBold.otf') format('opentype');
  font-weight: 600;
}
@font-face {
  font-family: 'Bell-MT';
  src: url('./Bell/BellMTStd-SemiBold-Italic.otf') format('opentype');
  font-weight: 600;
  font-style: italic;
}
@font-face {
  font-family: 'Bell-MT';
  src: url('./Bell/BellMTStd-Bold.otf') format('opentype');
  font-weight: 700;
}
@font-face {
  font-family: 'Bell-MT';
  src: url('./Bell/BellMTStd-Bold-Italic.otf') format('opentype');
  font-weight: 700;
  font-style: italic;
}
