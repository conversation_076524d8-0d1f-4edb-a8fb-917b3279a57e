import { StrictMode } from 'react';
import { createRoot } from 'react-dom/client';

import App from './App.jsx';
import GlobalStyles from './utils/global_styles';
import { configureAmplify } from './utils/aws_config';
import { NetworkStatusProvider } from './hooks/NetworkStatusContext.jsx';
import DSNoInternet from './components/DSNoInternet.jsx'; // Adjust import path

configureAmplify();

createRoot(document.getElementById('root')).render(
  <StrictMode>
    <NetworkStatusProvider>
      <DSNoInternet /> {/* Conditionally rendered based on network status */}
      <GlobalStyles />
      <App /> {/* Main app that remains functional */}
    </NetworkStatusProvider>
  </StrictMode>,
);
