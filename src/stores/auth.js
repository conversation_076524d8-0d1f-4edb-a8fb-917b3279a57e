import localforage from 'localforage';

const SESSION_KEY = '__dsl_session';
const RESET_PASSWORD_STATUS_KEY = '__dsl_reset_password_status';

let _authStore;
const getAuthStore = () => {
  if (!_authStore) {
    _authStore = localforage.createInstance({
      driver: localforage.INDEXEDDB,
      name: 'dsl_cc_auth',
    });
  }
  return _authStore;
};

export const setToken = async (token) => {
  const store = getAuthStore();
  return store.setItem(SESSION_KEY, token);
};

export const getToken = async () => {
  const store = getAuthStore();
  return store.getItem(SESSION_KEY);
};

export const removeToken = async () => {
  const store = getAuthStore();
  return store.removeItem(SESSION_KEY);
};

export const setResetPasswordStatus = async (status) => {
  const store = getAuthStore();
  return store.setItem(RESET_PASSWORD_STATUS_KEY, status);
};

export const getResetPasswordStatus = async () => {
  const store = getAuthStore();
  return store.getItem(RESET_PASSWORD_STATUS_KEY);
};

export const removeResetPasswordStatus = async () => {
  const store = getAuthStore();
  return store.removeItem(RESET_PASSWORD_STATUS_KEY);
};
