name: Deploy

on:
  push:
    branches:
      - develop
  workflow_dispatch:
    inputs:
      git-ref:
        description: Git Ref (Optional)
        required: false

jobs:
  deployment:
    runs-on: ubuntu-latest
    environment: preview
    permissions:
      id-token: write # This is required for requesting the JWT for AWS
      contents: read  # This is required for actions/checkout for AWS

    steps:
      - name: Checkout the Repo (Latest)
        uses: actions/checkout@v4
        if: github.event.inputs.git-ref == ''

      - name: Checkout the Repo (Custom Ref)
        uses: actions/checkout@v4
        if: github.event.inputs.git-ref != ''
        with:
          ref: ${{ github.event.inputs.git-ref }}

      - name: Setup Outputs
        id: args
        # FORMATTED_DATE is used in the construction of our version label in beanstalk
        # SHORT_SHA is used in the construction of our version label in beanstalk
        # PACKAGE_VERSION is retreived from the package.json file. Also used in the construction of the version label.
        run: |
          FORMATTED_DATE=$(echo $(date +'%Y%m%d_%H%M%S'))
          SHORT_SHA=$(echo ${GITHUB_SHA} | cut -c1-4)
          PACKAGE_VERSION=$(cat ./package.json | grep version | head -1 | awk -F: '{ print $2 }' | sed 's/[",]//g' | tr -d '[[:space:]]' | sed 's/\./_/g' | tr -d '[[:space:]]')
          echo "version_label=$(echo gha_run-$PACKAGE_VERSION-${{ github.run_number }}-${SHORT_SHA}-${FORMATTED_DATE})" >> $GITHUB_OUTPUT
          echo "commit_message=$(git log -1 --pretty=format:"%s")" >> $GITHUB_OUTPUT

      - name: Setup NodeJS
        uses: actions/setup-node@v3
        with:
          node-version: 22
      
      # Creates a Zip file containing only what we need deployed to the server. The output zip will exclude any .env files,
      # any node_modules that may exist, and the contents of the .git folder.
      - name: Create Zip for upload
        run: zip -r deploy.zip . -x '.env*' -x 'node_modules/*' -x '.git/*'

      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: ${{ secrets.AWS_IAM_ROLE_ARN }}
          aws-region: ${{ vars.AWS_REGION }}

      # Using the constructed version_label, we upload the zipped directory to the storage bucket allocated for the Beanstalk app.
      - name: Upload package to S3
        run: |
          aws s3 cp deploy.zip "s3://${{ vars.S3_BUCKET_NAME }}/${{ vars.EB_APP_NAME }}/${{ steps.args.outputs.version_label }}.zip"

      # Use the aws CLI to create a new application version referencing our newly uploaded bundle.
      - name: Create new ElasticBeanstalk Application Version
        run: |
          description=$(git log -1 --pretty=%B)
          if [ ${#description} -ge 200 ]
          then
            description="${description:0:200-3}..."
          fi

          aws elasticbeanstalk create-application-version \
          --application-name "${{ vars.EB_APP_NAME }}" \
          --source-bundle S3Bucket="${{ vars.S3_BUCKET_NAME }}",S3Key="${{ vars.EB_APP_NAME }}/${{ steps.args.outputs.version_label }}.zip" \
          --version-label "${{ steps.args.outputs.version_label }}" \
          --description "${description}"

      # Update the beanstalk environment to deploy the new version.
      - name: Deploy new ElasticBeanstalk Application Version
        run: |
          aws elasticbeanstalk update-environment \
          --environment-name ${{ vars.EB_ENV_NAME }} \
          --version-label "${{ steps.args.outputs.version_label }}"