import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import nodePolyfills from 'rollup-plugin-polyfill-node';
import eslint from 'vite-plugin-eslint';

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [
    react({
      babel: {
        plugins: ['babel-plugin-macros', 'babel-plugin-styled-components'],
      },
    }),
    eslint({
      exclude: ['/virtual:/**', 'node_modules/**'],
    }),
  ],
  optimizeDeps: {
    esbuildOptions: {
      // Node.js global to browser globalThis
      define: {
        global: 'globalThis', //<--- AWS SDK
      },
    },
  },
  build: {
    rollupOptions: {
      // Enable rollup polyfills plugin
      // used during production bundling
      plugins: [nodePolyfills()],
    },
  },
  resolve: {
    alias: {
      // added for AWS-SDK with vite
      // https://github.com/vitejs/vite/issues/9445
      './runtimeConfig': './runtimeConfig.browser',
    },
  },
  server: {
    allowedHosts: ['.medlplayground.com'],
  },
});
