/** @type {import('tailwindcss').Config} */
import lineClamp from '@tailwindcss/line-clamp';

export default {
  content: ['./src/**/*.{js,ts,jsx,tsx}'],
  theme: {
    extend: {
      colors: {
        dslGray: {
          1: '#ECECEC',
          2: '#E2E2E2',
          3: '#A6A6A6',
          4: '#6B6B6B',
          5: '#2E2E2E',
        },
        dslBlack: {
          1: '#000000',
          2: '#1B1B1B',
          faded: {
            5: 'rgb(0, 0, 0, 0.05)',
            25: 'rgb(0, 0, 0, 0.25)',
            35: 'rgb(0, 0, 0, 0.35)',
            50: 'rgb(0, 0, 0, 0.5)',
            70: 'rgb(0, 0, 0, 0.70)',
            75: 'rgb(0, 0, 0, 0.75)',
          },
        },
        dslWhite: '#FFFFFF',
        dslBlue: {
          dark: '#2E3191',
          darkHover: '#212475',
          light: '#DDDEFF',
          lightFaded: 'rgba(221, 222, 255, 0.5)',
          normal: '#00B8F1',
        },
        dslGreen: '#4E912E',
        dslBrown: '#915E2E',
        dslStatus: {
          red: '#912E2E',
          redLight: '#FFDDDD',
          blue: '#0D4874',
          blueLight: '#DDF1FF',
          green: '#235107',
          greenLight: '#D3FFB7',
          yellow: '#5C4808',
          yellowLight: '#FBE6A6',
          purple: '#4D0D74',
          purpleLight: '#E4B8FF',
          orange: '#74380D',
          orangeLight: '#FFCDA9',
        },
      },
      fontSize: {
        '2xs': [
          '0.625rem',
          {
            lineHeight: '0.75rem',
          },
        ],
      },
      boxShadow: {
        dslMedium: '0px 4px 15px rgba(0, 0, 0, 0.15)',
        dslLarge: '0px 10px 15px rgba(0, 0, 0, 0.3)',
        dslLargeDescend: '0px 1px 3px rgba(0, 0, 0, 0.15)',
        dslSecondary: '0px 1px 2px rgba(0, 0, 0, 0.05)',
      },
      fontFamily: {
        sans: ['Brother-1816', 'sans-serif'],
        serif: ['Bell-MT', 'serif'],
      },
      spacing: {
        7.5: '1.875rem',
      },
      backgroundImage: {
        galaxy: 'url(/src/assets/galaxy-bkg.jpg)',
      },
      animation: {
        'spin-slow': 'spin 10s linear infinite',
      },
      screens: {
        xs: '320px',
        '3xl': '2100px',
      },
    },
  },
  plugins: [lineClamp],
};
