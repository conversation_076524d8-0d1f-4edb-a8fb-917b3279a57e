{
  // Use IntelliSense to learn about possible attributes.
  // Hover to view descriptions of existing attributes.
  // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
  "version": "0.2.0",
  "configurations": [
    {
      "name": "Run Storybook",
      "request": "launch",
      "runtimeArgs": [
        "run-script",
        "storybook"
      ],
      "runtimeExecutable": "npm",
      "skipFiles": [
        "<node_internals>/**"
      ],
      "type": "node",
      "outputCapture": "std"
    },
    {
      "name": "Run MRM",
      "request": "launch",
      "runtimeArgs": [
        "run-script",
        "dev:backend"
      ],
      "runtimeExecutable": "npm",
      "skipFiles": [
        "<node_internals>/**"
      ],
      "envFile": "${workspaceFolder}/.env.server",
      "type": "node",
      "outputCapture": "std"
    }
  ]
}