import express from 'express';

import { MRMApi } from '../../../lib/api.js';
import verifyJWT from '../../../middleware/jwt_authorizer.js';

const API = MRMApi.createInstance({
  baseUrl: process.env.API_BASE_URL,
  apiKey: process.env.API_KEY,
  siteId: '',
});

const router = express.Router({ mergeParams: true });

/* GET home page. */
router.get('/', (req, res) => {
  res.status(200);
  return res.json({
    status: 'OK',
    message: 'Welcome to the Access API Service',
  });
});

router.get('/site/:siteId/info', verifyJWT, async (req, res) => {
  const token = res.locals?.token;
  const siteId = req.params.siteId;
  const response = await API.getSiteGroupInfo(token, siteId);
  res.status(200);
  return res.json(response);
});

router.get('/site/:siteId/mobile-sites', verifyJWT, async (req, res, next) => {
  try {
    const token = res.locals?.token;
    const { siteId } = req.params;
    const response = await API.getMobileSites(token, siteId);
    res.status(200);
    return res.json(response);
  } catch (error) {
    return next(error);
  }
});

router.get('/site/:siteId/locations', verifyJWT, async (req, res, next) => {
  try {
    const token = res.locals?.token;
    const { siteId } = req.params;
    const response = await API.getSiteGroupLocations(token, siteId);
    res.status(200);
    return res.json(response);
  } catch (error) {
    return next(error);
  }
});

router.get(
  '/site/:siteId/mobile-site/:mobileSiteId/schedules',
  verifyJWT,
  async (req, res, next) => {
    const token = res.locals?.token;
    const siteId = req.params.siteId;
    const mobileSiteId = req.params.mobileSiteId;
    try {
      const response = await API.getMobileSiteSchedules(
        token,
        siteId,
        mobileSiteId,
      );
      res.status(200);
      return res.json(response);
    } catch (err) {
      return next(err);
    }
  },
);

router.get(
  '/site/:siteId/mobile-site/:mobileSiteId/blackouts',
  verifyJWT,
  async (req, res, next) => {
    const token = res.locals?.token;
    const siteId = req.params.siteId;
    const mobileSiteId = req.params.mobileSiteId;
    try {
      const response = await API.getMobileSiteBlackouts(
        token,
        siteId,
        mobileSiteId,
      );
      res.status(200);
      return res.json(response);
    } catch (err) {
      return next(err);
    }
  },
);

router.put(
  '/site/:siteId/mobile-site/:mobileSiteId/schedule',
  verifyJWT,
  async (req, res, next) => {
    try {
      const token = res.locals?.token;
      const { siteId, mobileSiteId } = req.params;
      const response = await API.createSchedule(
        token,
        siteId,
        mobileSiteId,
        req.body,
      );
      res.status(200);
      return res.json(response);
    } catch (error) {
      return next(error);
    }
  },
);

router.delete('/site/:siteId/mobile-site/:mobileSiteId/schedule/:locationScheduleId', verifyJWT, async (req, res, next) => {
  const { siteId, mobileSiteId, locationScheduleId } = req.params;
  const { token } = res.locals;

  try {
    const response = await API.removeScheduledSession(token, siteId, mobileSiteId, locationScheduleId);
    res.status(200);
    return res.json(response);
  } catch (err) {
    return next(err);
  }
});

export default router;
