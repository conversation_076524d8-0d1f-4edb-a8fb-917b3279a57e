import express from 'express';

import { MRMAccessApi } from '../../../lib/api.js';
import verifyJWT from '../../../middleware/jwt_authorizer.js';

const API = MRMAccessApi.createInstance({
  baseUrl: process.env.API_BASE_URL,
  apiKey: process.env.API_KEY,
});

const router = express.Router({ mergeParams: true });

/* GET home page. */
router.get('/', (req, res) => {
  res.status(200);
  return res.json({
    status: 'OK',
    message: 'Welcome to the Access API Service',
  });
});

router.get('/sites', verifyJWT, async (req, res) => {
  const token = res.locals?.token;
  const response = await API.getAllSites(token);
  console.log('sites response', response);
  res.status(200);
  return res.json(response);
});

export default router;
