import express from 'express';

import mrmAccessApiRoutes from './mrm-access/index.js';
import mrmApiRoutes from './mrm/index.js';
import commonApiRoutes from './common/index.js';

const router = express.Router({ mergeParams: true });

/* GET home page. */
router.get('/', (req, res) => {
  res.status(200);
  return res.json({
    status: 'OK',
    message: 'Welcome to the API',
  });
});

router.use('/mrm-access', mrmAccessApiRoutes);
router.use('/mrm', mrmApiRoutes);
router.use('/common', commonApiRoutes);

export default router;
