import express from 'express';

import { MRMAccessApi } from '../../../lib/api.js';
import verifyJWT from '../../../middleware/jwt_authorizer.js';

const API = MRMAccessApi.createInstance({
  baseUrl: process.env.API_BASE_URL,
  apiKey: process.env.API_KEY,
});

const router = express.Router({ mergeParams: true });

/* GET home page. */
router.get('/', (req, res) => {
  res.status(200);
  return res.json({
    status: 'OK',
    message: 'Welcome to the Access API Service',
  });
});

router.get('/profile', verifyJWT, async (req, res) => {
  const token = res.locals?.token;
  const response = await API.getAuthenticatedUserInfo(token);
  res.status(200);
  return res.json(response);
});

router.get('/site/:site_id/users', verifyJWT, async (req, res) => {
  const token = res.locals?.token;
  const { site_id } = req.params;
  const response = await API.getUserlist(token, site_id);
  res.status(200);
  return res.json(response);
});

router.put('/site/:site_id/user', verifyJWT, async (req, res, next) => {
  try {
    const token = res.locals?.token;
    const { site_id } = req.params;
    const data = req.body;
    const response = await API.addNewUser(token, data, site_id);
    res.status(200);
    return res.json(response);
  } catch (error) {
    next(error);
  }
});

router.patch(
  '/site/:site_id/user/:user_site_roles_id',
  verifyJWT,
  async (req, res, next) => {
    try {
      const token = res.locals?.token;
      const { site_id } = req.params;
      const { user_site_roles_id } = req.params;
      const data = req.body;
      const response = await API.updateUserRole(
        token,
        data,
        site_id,
        user_site_roles_id,
      );
      res.status(200);
      return res.json(response);
    } catch (error) {
      return next(error);
    }
  },
);

router.delete(
  '/site/:site_id/user/:user_site_roles_id',
  verifyJWT,
  async (req, res, next) => {
    try {
      const token = res.locals?.token;
      const { site_id } = req.params;
      const { user_site_roles_id } = req.params;
      const response = await API.deleteUserRole(
        token,
        site_id,
        user_site_roles_id,
      );
      res.status(200);
      return res.json(response);
    } catch (error) {
      return next(error);
    }
  },
);

router.get('/users/super-admin', verifyJWT, async (req, res, next) => {
  try {
    const token = res.locals?.token;
    const response = await API.getSuperAdminUserlist(token);
    res.status(200);
    return res.json(response);
  } catch (error) {
    return next(error);
  }
});

router.put('/user/super-admin', verifyJWT, async (req, res, next) => {
  try {
    const token = res.locals?.token;
    const data = req.body;
    const response = await API.addUpdateSuperAdmin(token, data);
    res.status(200);
    return res.json(response);
  } catch (error) {
    next(error);
  }
});

router.post('/:site_id/invite/resend', verifyJWT, async (req, res, next) => {
  try {
    const token = res.locals?.token;
    const { site_id } = req.params;
    const data = req.body;
    const response = await API.resendInvitation(token, data, site_id);
    res.status(200);
    return res.json(response);
  } catch (error) {
    next(error);
  }
});

router.delete(
  '/user/super-admin/:user_site_roles_id',
  verifyJWT,
  async (req, res, next) => {
    try {
      const token = res.locals?.token;
      const { user_site_roles_id } = req.params;
      const response = await API.deleteUserRole(token, user_site_roles_id);
      res.status(200);
      return res.json(response);
    } catch (error) {
      return next(error);
    }
  },
);

export default router;
