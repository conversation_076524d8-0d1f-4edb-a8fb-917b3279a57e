import { verifyToken } from '../lib/aws_jwt_verify.js';
import { findJwt, parseUserMetaFromJWT } from '../lib/util.js';

const verifyJWT = async (req, res, next) => {
  const reqApiToken = req.headers['authorization'];
  const jwt = findJwt(reqApiToken);
  
  // Cognito Handling
  const payload = await verifyToken(jwt);

  if (!payload) {
    return res.status(401).json({ message: 'Unauthorized' });
  }
  
  const userMeta = parseUserMetaFromJWT(payload);
  if (!userMeta || !userMeta.user_id) {
    return res.status(401).json({ message: 'Unauthorized' });
  }

  res.locals.userMeta = userMeta;
  res.locals.token = reqApiToken;
  return next();
}

export default verifyJWT;
