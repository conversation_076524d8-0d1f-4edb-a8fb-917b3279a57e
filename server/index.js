import express from 'express';
import ViteExpress from 'vite-express';
import cors from 'cors';

import apiRoutes from './routes/api/index.js';

const PORT = process.env.PORT || 3000;

const app = express();

// CORS
app.use(cors());

app.use(express.json());
app.use(express.urlencoded({ extended: false }));

// START API ROUTES
app.use('/api', apiRoutes);
// END API ROUTES

// Error Handler
app.use((err, req, res, next) => {
  const errorRes = {
    message: err.message,
    stack: process.env.DEBUG ? err.stack : undefined,
  };

  console.error(errorRes);
  res.status(err.status || 500);
  res.json(errorRes);
});

// Start ViteExpress that will serve the Vite app
ViteExpress.listen(app, PORT, () => console.log(`Server listening on ${PORT}`));
