import axios from 'axios';

/**
 * Formats the authorization header for the external MRM API.
 * The external API expects "Bearer token=<jwt>" format instead of "Bearer <jwt>".
 *
 * @param {string} authHeader - The authorization header from the client (e.g., "Bearer <jwt>")
 * @returns {string} - The formatted authorization header for the external API
 */
const formatAuthHeaderForExternalAPI = (authHeader) => {
  if (!authHeader) {
    return '';
  }

  // If it's already in the correct format, return as-is
  if (authHeader.includes('token=')) {
    return authHeader;
  }

  // Extract the JWT from "Bearer <jwt>" format
  const [scheme, jwt] = authHeader.split(' ');

  if (scheme === 'Bearer' && jwt) {
    // Format as "Bearer token=<jwt>" for the external API
    return `Bearer token=${jwt}`;
  }

  // If it's just a raw JWT, format it properly
  return `Bearer token=${authHeader}`;
};

class MobileResourceManagerApi {
  config;
  mrmApi;

  constructor({ baseUrl, apiKey, siteId }) {
    this.config = {
      baseUrl,
      apiKey,
      siteId,
    };

    this.mrmApi = axios.create({
      baseURL: baseUrl,
      responseType: 'json',
      headers: {
        'x-api-key': apiKey,
      },
    });

    // Intercept responses
    this.mrmApi.interceptors.response.use(
      (response) => {
        return response;
      },
      (error) => {
        const errorResponse = error.response || {};

        return Promise.reject({
          status: errorResponse.status,
          message: errorResponse.data?.message || 'An error occurred',
        });
      },
    );
  }
}

export class MRMAccessApi extends MobileResourceManagerApi {
  constructor(config) {
    super(config);
  }

  static createInstance(config) {
    return new MRMAccessApi(config);
  }

  /**
   *
   * @param {string} idToken A Cognito ID token
   * @returns {Promise<UserInfio>}
   */
  getAuthenticatedUserInfo = async (idToken) => {
    const response = await this.mrmApi.get(`mrm-access/profile`, {
      headers: {
        Authorization: formatAuthHeaderForExternalAPI(idToken),
      },
    });
    return response.data;
  };

  /**
   *
   * @param {string} idToken A Cognito ID token
   * @returns {Promise<UserInfio>}
   */
  getUserlist = async (idToken, site_id) => {
    const response = await this.mrmApi.get(
      `/mrm-access/site/${site_id}/users`,
      {
        headers: {
          Authorization: formatAuthHeaderForExternalAPI(idToken),
        },
      },
    );
    return response.data;
  };

  /**
   *
   * @param {string} idToken
   * @param {Object} data
   * @param {string} data.email
   * @param {string} data.role - One of ('admin', 'instructor', 'student', 'pod_operator')
   * @returns
   */
  addNewUser = async (idToken, data, site_id) => {
    const reqData = {
      user_email: data.user_email,
      mrm_role: data.mrm_role,
    };
    const response = await this.mrmApi.put(
      `/mrm-access/site/${site_id}/user`,
      reqData,
      {
        headers: {
          Authorization: formatAuthHeaderForExternalAPI(idToken),
        },
      },
    );
    return response.data;
  };

  updateUserRole = async (idToken, data, site_id, user_site_roles_id) => {
    const reqData = {
      mrm_role: data.mrm_role,
    };

    const response = await this.mrmApi.patch(
      `/mrm-access/site/${site_id}/user/${user_site_roles_id}`,
      reqData,
      {
        headers: {
          Authorization: formatAuthHeaderForExternalAPI(idToken),
        },
      },
    );
    return response.data;
  };

  deleteUserRole = async (idToken, site_id, user_site_roles_id) => {
    const response = await this.mrmApi.delete(
      `/mrm-access/site/${site_id}/user/${user_site_roles_id}`,
      {
        headers: {
          Authorization: formatAuthHeaderForExternalAPI(idToken),
        },
      },
    );
    return response.data;
  };

  getSuperAdminUserlist = async (idToken) => {
    const response = await this.mrmApi.get(`/mrm-access/users/super-admin`, {
      headers: {
        Authorization: formatAuthHeaderForExternalAPI(idToken),
      },
    });
    return response.data;
  };

  addUpdateSuperAdmin = async (idToken, data) => {
    const reqData = {
      user_email: data.user_email,
    };

    const response = await this.mrmApi.put(
      `/mrm-access/user/super-admin`,
      reqData,
      {
        headers: {
          Authorization: formatAuthHeaderForExternalAPI(idToken),
        },
      },
    );
    return response.data;
  };

  resendInvitation = async (idToken, data, site_id) => {
    const reqData = {
      email: data.email,
    };
    const response = await this.mrmApi.post(
      `/mrm-access/${site_id}/invite/resend`,
      reqData,
      {
        headers: {
          Authorization: formatAuthHeaderForExternalAPI(idToken),
        },
      },
    );
    return response.data;
  };

  deleteSuperAdmin = async (idToken, user_site_roles_id) => {
    const response = await this.mrmApi.delete(
      `/mrm-access/user/super-admin/${user_site_roles_id}`,
      {
        headers: {
          Authorization: formatAuthHeaderForExternalAPI(idToken),
        },
      },
    );
    return response.data;
  };

  /**
   * Get all sites for the authenticated user
   * @param {string} idToken A Cognito ID token
   * @returns {Promise<Array>} Array of sites
   */
  getAllSites = async (idToken) => {
    const response = await this.mrmApi.get(`/mrm-access/sites`, {
      headers: {
        Authorization: formatAuthHeaderForExternalAPI(idToken),
      },
    });
    return response.data;
  };
}

export class MRMApi extends MobileResourceManagerApi {
  constructor(config) {
    super(config);
  }

  static createInstance(config) {
    return new MRMApi(config);
  }

  getSiteGroupInfo = async (idToken, siteId) => {
    const response = await this.mrmApi.get(`/mrm/site/${siteId}/info`, {
      headers: {
        Authorization: formatAuthHeaderForExternalAPI(idToken),
      },
    });
    return response.data;
  };

  getMobileSites = async (idToken, siteId) => {
    const response = await this.mrmApi.get(`mrm/site/${siteId}/mobile-sites`, {
      headers: {
        Authorization: formatAuthHeaderForExternalAPI(idToken),
      },
    });
    return response.data;
  };

  getSiteGroupLocations = async (idToken, siteId) => {
    const response = await this.mrmApi.get(`mrm/site/${siteId}/locations`, {
      headers: {
        Authorization: formatAuthHeaderForExternalAPI(idToken),
      },
    });
    return response.data;
  };

  getMobileSiteSchedules = async (idToken, siteId, mobileSiteId) => {
    const response = await this.mrmApi.get(
      `mrm/site/${siteId}/mobile-site/${mobileSiteId}/schedules`,
      {
        headers: {
          Authorization: formatAuthHeaderForExternalAPI(idToken),
        },
      },
    );
    return response.data;
  };

  getMobileSiteBlackouts = async (idToken, siteId, mobileSiteId) => {
    const response = await this.mrmApi.get(
      `mrm/site/${siteId}/mobile-site/${mobileSiteId}/blackouts`,
      {
        headers: {
          Authorization: formatAuthHeaderForExternalAPI(idToken),
        },
      },
    );
    return response.data;
  };

  createSchedule = async (idToken, siteId, mobileSiteId, reqData) => {
    const response = await this.mrmApi.put(
      `/mrm/site/${siteId}/mobile-site/${mobileSiteId}/schedule`,
      reqData,
      {
        headers: {
          Authorization: formatAuthHeaderForExternalAPI(idToken),
        },
      },
    );
    return response.data;
  };

  /**
   *
   * @param {string} idToken
   * @param {string} siteId
   * @param {string} mobileSiteId
   * @param {string} locationScheduleId
   * @returns
   */
  removeScheduledSession = async (idToken, siteId, mobileSiteId, locationScheduleId) => {
    const queryParams = {
      force: true,
    };
    const response = await this.mrmApi.delete(
      `/mrm/site/${siteId}/mobile-site/${mobileSiteId}/schedule/${locationScheduleId}`,
      {
        headers: {
          Authorization: formatAuthHeaderForExternalAPI(idToken),
        },
        params: queryParams,
      },
    );
    return response.data;
  };
}

export default {
  MRMAccessApi,
  MRMApi,
};

// TODO: Add JSDoc comments for API types.
