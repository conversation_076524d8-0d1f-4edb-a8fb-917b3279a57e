/**
 * A function that extracts the JWT from the Authorization header. Supports
 * both the "Bearer <token>" and "<token>" formats.
 * 
 * @param {string} headerValue The value of the Authorization header
 * @returns {string} The JWT
 */
export const findJwt = (headerValue) => {
  if (!headerValue) {
    return '';
  }

  const [scheme, token] = headerValue.split(' ');
  const jwt = token || scheme;

  if (jwt === 'Bearer') {
    return '';
  }

  return jwt;
};

export const findAuthorizationScheme = (headerValue) => {
  if (!headerValue) {
    return;
  }

  const [scheme] = headerValue.split(' ');
  return scheme;
};

/**
 * A function that parses the user metadata from the JWT payload.
 * 
 * @param {object} jwtPayload
 * @returns {object} The metadata object
 */
export const parseUserMetaFromJWT = (jwtPayload) => {
  if (!jwtPayload) {
    return {};
  };

  const {
    'cognito:groups': cognito_groups = [],
    sub,
  } = jwtPayload;

  return {
    cognito_groups: cognito_groups.join('|'),
    user_id: sub,
  };
};

export default {
  findJwt,
  parseUserMetaFromJWT,
};