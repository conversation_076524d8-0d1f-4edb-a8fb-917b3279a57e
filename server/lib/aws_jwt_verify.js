import { CognitoJwtVerifier } from 'aws-jwt-verify';

const userPoolId = process.env.AWS_USER_POOLS_ID || '';
const clientId = process.env.AWS_USER_POOLS_WEB_CLIENT_ID || '';

const verifier = CognitoJwtVerifier.create({
  userPoolId,
  tokenUse: 'id',
  clientId,
});

/**
 * 
 * @param {*} token 
 * @returns 
 */
export const verifyToken = async (token) => {
  try {
    const payload = await verifier.verify(token);
    return payload;
  } catch (err) {
    console.log('Token not valid: ', err);
  }
};

export default {
  verifyToken,
};
