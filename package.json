{"name": "dsl-mobile-resource-manager", "private": true, "babelMacros": {"twin": {"preset": "styled-components"}}, "version": "0.0.0", "type": "module", "scripts": {"start": "node ./server/index.js", "dev:frontend": "vite", "dev:backend": "nodemon -r dotenv/config ./server/index.js dotenv_config_path=./.env.server -V", "dev": "vite", "build": "vite build", "lint": "eslint -c eslint.config.js --ext .js,.jsx,.cjs,.mjs ./src --report-unused-disable-directives --max-warnings 0", "lint:fix": "npm run lint -- --fix", "prettier": "prettier src --check", "prettier:fix": "npm run prettier -- --write", "format": "npm run prettier:fix && npm run lint:fix", "preview": "vite preview", "prepare": "husky install", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build", "chromatic": "npx chromatic --project-token=chpt_bd0cb54e21b8d86", "test": "echo \"No tests configured yet\""}, "dependencies": {"@fullcalendar/core": "^6.1.17", "@fullcalendar/interaction": "^6.1.17", "@fullcalendar/luxon3": "^6.1.17", "@fullcalendar/premium-common": "^6.1.17", "@fullcalendar/react": "^6.1.17", "@fullcalendar/resource": "^6.1.17", "@fullcalendar/resource-daygrid": "^6.1.17", "@fullcalendar/resource-timegrid": "^6.1.17", "@fullcalendar/resource-timeline": "^6.1.17", "@fullcalendar/scrollgrid": "^6.1.17", "@fullcalendar/timeline": "^6.1.17", "@tailwindcss/line-clamp": "^0.4.4", "@tanstack/react-query": "~5.67.1", "aws-amplify": "^6.14.4", "aws-jwt-verify": "^5.1.0", "axios": "^1.9.0", "cors": "~2.8.5", "crypto-js": "~4.2.0", "express": "~4.21.1", "js-cookie": "~3.0.5", "localforage": "~1.10.0", "lodash": "^4.17.21", "luxon": "^3.6.1", "react": "^19.1.0", "react-data-table-component": "^7.7.0", "react-datepicker": "^8.3.0", "react-dom": "^19.1.0", "react-router-dom": "^6.30.0", "styled-components": "~5.3.0", "twin.macro": "~3.4.1", "uuid": "^11.1.0", "vite-express": "^0.21.1"}, "optionalDependencies": {"husky": "~9.1.7"}, "devDependencies": {"@eslint/compat": "~1.2.7", "@eslint/eslintrc": "~3.3.0", "@eslint/js": "^9.25.0", "@storybook/addon-essentials": "~8.6.3", "@storybook/addon-interactions": "~8.6.3", "@storybook/addon-links": "~8.6.3", "@storybook/blocks": "~8.6.3", "@storybook/builder-vite": "~8.5.1", "@storybook/react": "~8.6.3", "@storybook/react-vite": "~8.6.3", "@storybook/testing-library": "~0.2.2", "@vitejs/plugin-react": "^4.4.1", "autoprefixer": "~10.4.20", "babel-plugin-macros": "~3.1.0", "babel-plugin-styled-components": "~2.1.4", "chromatic": "~11.26.1", "dotenv": "~16.4.7", "eslint": "^9.25.0", "eslint-config-prettier": "~10.1.5", "eslint-plugin-jsdoc": "~50.6.17", "eslint-plugin-prefer-arrow": "~1.2.3", "eslint-plugin-react": "~7.37.4", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "eslint-plugin-storybook": "~0.11.3", "globals": "^16.0.0", "lint-staged": "~15.4.3", "nodemon": "~3.1.9", "postcss": "~8.5.3", "prettier": "~3.5.2", "prop-types": "~15.8.1", "rollup-plugin-polyfill-node": "~0.13.0", "storybook": "~8.6.3", "tailwindcss": "^3.4.17", "vite": "^6.3.5", "vite-plugin-eslint": "~1.8.1"}, "engines": {"node": ">=22.0.0"}}